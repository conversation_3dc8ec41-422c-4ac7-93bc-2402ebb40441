@if (ShowContainer)
{
    <div class="@ContainerClass">
        @ButtonsContent
    </div>
}
else
{
    @ButtonsContent
}

@code {
    private RenderFragment ButtonsContent => __builder =>
    {
        @if (ShowFirstButton)
        {
            <button class="@GetFirstButtonClass()" 
                    @onclick="OnFirstClick" 
                    disabled="@IsFirstDisabled"
                    title="@FirstTitle">
                @if (!string.IsNullOrEmpty(FirstIcon))
                {
                    <i class="@FirstIcon" style="color: inherit;"></i>
                }
                @if (ShowActionButtonLabel && !string.IsNullOrEmpty(FirstText))
                {
                    <span class="d-none d-lg-inline @(!string.IsNullOrEmpty(FirstIcon) ? "ms-1" : "")">@FirstText</span>
                }
            </button>
        }

        @if (ShowSecondButton)
        {
            <button class="@GetSecondButtonClass()" 
                    @onclick="OnSecondClick" 
                    disabled="@IsSecondDisabled"
                    title="@SecondTitle">
                @if (!string.IsNullOrEmpty(SecondIcon))
                {
                    <i class="@SecondIcon" style="color: inherit;"></i>
                }
                @if (ShowActionButtonLabel && !string.IsNullOrEmpty(SecondText))
                {
                    <span class="d-none d-lg-inline @(!string.IsNullOrEmpty(SecondIcon) ? "ms-1" : "")">@SecondText</span>
                }
            </button>
        }

        @if (ShowThirdButton)
        {
            <button class="@GetThirdButtonClass()" 
                    @onclick="OnThirdClick" 
                    disabled="@IsThirdDisabled"
                    title="@ThirdTitle">
                @if (!string.IsNullOrEmpty(ThirdIcon))
                {
                    <i class="@ThirdIcon" style="color: inherit;"></i>
                }
                @if (ShowActionButtonLabel && !string.IsNullOrEmpty(ThirdText))
                {
                    <span class="d-none d-lg-inline @(!string.IsNullOrEmpty(ThirdIcon) ? "ms-1" : "")">@ThirdText</span>
                }
            </button>
        }

        @if (ShowFourthButton)
        {
            <button class="@GetFourthButtonClass()" 
                    @onclick="OnFourthClick" 
                    disabled="@IsFourthDisabled"
                    title="@FourthTitle">
                @if (!string.IsNullOrEmpty(FourthIcon))
                {
                    <i class="@FourthIcon" style="color: inherit;"></i>
                }
                @if (ShowActionButtonLabel && !string.IsNullOrEmpty(FourthText))
                {
                    <span class="d-none d-lg-inline @(!string.IsNullOrEmpty(FourthIcon) ? "ms-1" : "")">@FourthText</span>
                }
            </button>
        }
    };
}
