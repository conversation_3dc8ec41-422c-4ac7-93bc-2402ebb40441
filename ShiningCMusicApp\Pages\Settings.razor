@page "/settings"
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicCommon.Extensions
@using ShiningCMusicCommon.Constants
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns

@attribute [RequireLevel100Access]

<PageTitle>Settings - ShiningC Music</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Settings</h2>
            <p class="text-muted">Manage application configuration settings</p>

            @if (!isLoading && !CanEditSettings)
            {
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> You have read-only access to system settings. Contact an administrator to make changes.
                </div>
            }
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading settings...</p>
        </div>
    }
    else if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Configuration Error</h4>
                    <p>@errorMessage</p>
                    <hr>
                    <p class="mb-0">Please ensure that:</p>
                    <ul>
                        <li>The database is running and accessible</li>
                        <li>The Config table has been created (run the SQL migration script)</li>
                        <li>The API service is running and responding correctly</li>
                    </ul>
                </div>
            </div>
        </div>
    }
    else if (configGroups?.Any() == true)
    {
        <div class="row">
            <div class="col-12">
                <SfTab @bind-SelectedItem="selectedTabIndex" SwipeMode="TabSwipeMode.Mouse">
                    <TabItems>
                        @foreach (var group in configGroups.OrderBy(g => g.GroupId))
                        {
                            <TabItem>
                                <ChildContent>
                                    <TabHeader Text="@group.DisplayName" />
                                </ChildContent>
                                <ContentTemplate>
                                    <div class="p-3">
                                        @if (group.GroupId == (int)ConfigGroupId.BackgroundProcessors)
                                        {
                                            <div class="mb-3">
                                                <h5>Background Processors</h5>
                                                <p class="text-muted">Enable or disable background processing services</p>
                                                @RenderBackgroundProcessorToggles(group)
                                            </div>
                                        }
                                        @RenderConfigGroup(group)
                                    </div>
                                </ContentTemplate>
                            </TabItem>
                        }
                    </TabItems>
                </SfTab>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                    <button class="btn btn-primary" style="min-width: 200px;" @onclick="SaveChangesAsync"
                            disabled="@(!hasChanges || !CanEditSettings)">
                        <i class="bi bi-check-circle" style="color: white;"></i>
                        <span class="d-none d-sm-inline ms-2">Save Changes</span>
                        <span class="d-sm-none ms-2">Save</span>
                    </button>
                    <button class="btn btn-secondary" style="min-width: 200px;" @onclick="LoadConfigurationsAsync">
                        <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                        <span class="d-none d-sm-inline ms-2">Refresh</span>
                        <span class="d-sm-none ms-2">Refresh</span>
                    </button>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning">
                    <SfIcon Name="IconName.Warning" />
                    No configuration settings found.
                </div>
            </div>
        </div>
    }
</div>

@code {
    private RenderFragment RenderBackgroundProcessorToggles(ConfigGroup group)
    {
        return @<div class="row">
            @foreach (var processor in GetBackgroundProcessors().OrderBy(p => p.GetDisplayOrder()))
            {
                <div class="col-12 mb-3">
                    <div class="card @(HasProcessorConfigs(group, processor) ? "clickable-card" : "")">
                        <div class="card-header @(HasProcessorConfigs(group, processor) ? "clickable-header" : "")"
                             @onclick="@(() => ToggleProcessorConfig(processor))"
                             @onclick:stopPropagation="@(!HasProcessorConfigs(group, processor))"
                             style="@(HasProcessorConfigs(group, processor) ? "cursor: pointer;" : "")">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <h6 class="card-title mb-0 me-3">@processor.GetDisplayName()</h6>
                                    <div class="form-check form-switch" @onclick:stopPropagation="true">
                                        <input class="form-check-input" type="checkbox"
                                               checked="@backgroundProcessorStates[processor.ToConfigKey()]"
                                               disabled="@(!CanEditSettings)"
                                               @onchange="@((e) => OnBackgroundProcessorToggle(processor.ToConfigKey(), (bool)e.Value!))" />
                                        <label class="form-check-label">
                                            @(backgroundProcessorStates[processor.ToConfigKey()] ? "Enabled" : "Disabled")
                                        </label>
                                    </div>
                                </div>
                                @if (HasProcessorConfigs(group, processor))
                                {
                                    <div class="d-flex align-items-center">
                                        <small class="d-none d-sm-inline text-muted me-2">Click to configure</small>
                                        <i class="bi @(IsProcessorConfigExpanded(processor) ? "bi-chevron-up" : "bi-chevron-down")"></i>
                                    </div>
                                }
                            </div>
                            <small class="text-muted d-block mt-1">
                                @processor.GetDescription()
                            </small>
                        </div>

                        @if (HasProcessorConfigs(group, processor) && IsProcessorConfigExpanded(processor))
                        {
                            <div class="card-body border-top">
                                <h6 class="mb-3">Configuration Settings</h6>
                                @RenderProcessorConfigs(group, processor)
                            </div>
                        }
                    </div>
                </div>
            }
        </div>;
    }

    private RenderFragment RenderProcessorConfigs(ConfigGroup group, BackgroundProcessor processor)
    {
        var configs = GetProcessorConfigs(group, processor);

        if (!configs.Any())
            return @<div class="text-muted">No additional configuration options available.</div>;

        return @<div class="row">
            @foreach (var config in configs)
            {
                <div class="col-12 col-md-6 col-lg-4 mb-3">
                    <label class="form-label">
                        @(!string.IsNullOrEmpty(config.Description) ? config.Description : config.Key)
                    </label>

                    @if (config.DataType?.ToLower() == "bool")
                    {
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox"
                                   checked="@GetBoolValue(config.ConfigId)"
                                   disabled="@(!CanEditSettings)"
                                   @onchange="@((e) => SetBoolValue(config.ConfigId, (bool)e.Value!))" />
                            <label class="form-check-label">
                                @(GetBoolValue(config.ConfigId) ? "Enabled" : "Disabled")
                            </label>
                        </div>
                    }
                    else if (config.DataType?.ToLower() == "int")
                    {
                        <input type="number" class="form-control"
                               value="@GetIntValue(config.ConfigId)"
                               disabled="@(!CanEditSettings)"
                               min="1"
                               @onchange="@((e) => SetIntValue(config.ConfigId, int.Parse(e.Value?.ToString() ?? "0")))" />
                    }
                    else
                    {
                        <input type="text" class="form-control"
                               value="@configValues[config.ConfigId]"
                               disabled="@(!CanEditSettings)"
                               @onchange="@((e) => OnConfigValueChange(config.ConfigId, e.Value?.ToString() ?? ""))" />
                    }
                </div>
            }
        </div>;
    }

    private RenderFragment RenderConfigGroup(ConfigGroup group)
    {
        var nonProcessorConfigs = group.Configs.Where(c =>
            (group.GroupId != (int)ConfigGroupId.BackgroundProcessors ||
             (!c.Key.EndsWith("Enabled") && !IsProcessorSpecificConfig(c.Key))) &&
            c.Visible).ToList();

        if (!nonProcessorConfigs.Any())
            return@<div></div>;

        return@<div>
            <h5 class="mt-4">@group.DisplayName Configuration</h5>
            <div class="row">
                @foreach (var config in nonProcessorConfigs)
                {
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                        @if (!string.IsNullOrEmpty(config.Description))
                        {
                            <label class="form-label">@config.Description</label>
                            // <small class="text-muted d-block">@config.Description</small>
                        }
                        else
                        {
                            <label class="form-label">@config.Key</label>
                        }
                        @if (config.Key == CONFIG_SIDEBAR_THEME)
                        {
                            <SfDropDownList TValue="int" TItem="SidebarThemeOption"
                                          DataSource="@sidebarThemeOptions"
                                          Value="@GetIntValue(config.ConfigId)"
                                          ValueChanged="@((int value) => SetIntValue(config.ConfigId, value))"
                                          CssClass="form-control">
                                <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                                <DropDownListTemplates TItem="SidebarThemeOption">
                                    <ItemTemplate>
                                        <div class="d-flex align-items-center">
                                            <div class="sidebar-theme-preview me-2" style="background: @context.PreviewColor; width: 20px; height: 20px; border-radius: 3px;"></div>
                                            <span>@context.Text</span>
                                        </div>
                                    </ItemTemplate>
                                    <ValueTemplate>
                                        <div class="d-flex align-items-center">
                                            <div class="sidebar-theme-preview me-2" style="background: @context.PreviewColor; width: 20px; height: 20px; border-radius: 3px;"></div>
                                            <span>@context.Text</span>
                                        </div>
                                    </ValueTemplate>
                                </DropDownListTemplates>
                            </SfDropDownList>

                            @* Show custom color pickers when Custom theme is selected *@
                            @if (GetIntValue(config.ConfigId) == (int)SidebarTheme.Custom)
                            {
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Primary Color</label>
                                            <input type="color" class="form-control form-control-color"
                                                   value="@GetCustomColor1Value()"
                                                   disabled="@(!CanEditSettings)"
                                                   @onchange="@((e) => OnCustomColor1Change(e.Value?.ToString() ?? "#6c5ce7"))" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Secondary Color</label>
                                            <input type="color" class="form-control form-control-color"
                                                   value="@GetCustomColor2Value()"
                                                   disabled="@(!CanEditSettings)"
                                                   @onchange="@((e) => OnCustomColor2Change(e.Value?.ToString() ?? "#a29bfe"))" />
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else if (config.Key == CONFIG_ICON_STYLE)
                        {
                            <SfDropDownList TValue="string" TItem="string"
                                            DataSource="@iconStyles"
                                            Placeholder="Select a style"
                                            Value="@GetStringValue(config.ConfigId)"
                                            ValueChanged="@((string value) => OnConfigValueChange(config.ConfigId, value))"
                                            CssClass="form-control">
                            </SfDropDownList>
                        }
                        else if (config.Key == CONFIG_LESSON_TEXT_COLOR)
                        {
                            <SfDropDownList TValue="string" TItem="string"
                                            DataSource="@lessonTextColors"
                                            Placeholder="Select a color"
                                            Value="@GetStringValue(config.ConfigId)"
                                            ValueChanged="@((string value) => OnConfigValueChange(config.ConfigId, value))"
                                            CssClass="form-control">
                            </SfDropDownList>
                        }
                        else if (config.DataType?.ToLower() == "bool")
                        {
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox"
                                       checked="@GetBoolValue(config.ConfigId)"
                                       disabled="@(!CanEditSettings)"
                                       @onchange="@((e) => SetBoolValue(config.ConfigId, (bool)e.Value!))" />
                            </div>
                        }
                        else if (config.DataType?.ToLower() == "int")
                        {
                            <input type="number" class="form-control"
                                   value="@GetIntValue(config.ConfigId)"
                                   disabled="@(!CanEditSettings)"
                                   @onchange="@((e) => SetIntValue(config.ConfigId, int.Parse(e.Value?.ToString() ?? "0")))" />
                        }
                        else
                        {
                            <input type="text" class="form-control"
                                   value="@configValues[config.ConfigId]"
                                   disabled="@(!CanEditSettings)"
                                   @onchange="@((e) => OnConfigValueChange(config.ConfigId, e.Value?.ToString() ?? ""))" />
                        }

                    </div>
                }
            </div>
        </div>;
    }
}