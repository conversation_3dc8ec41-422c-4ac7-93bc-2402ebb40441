using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailQueueController : ControllerBase
    {
        private readonly IEmailQueueService _emailQueueService;
        private readonly ILogger<EmailQueueController> _logger;

        public EmailQueueController(IEmailQueueService emailQueueService, ILogger<EmailQueueController> logger)
        {
            _emailQueueService = emailQueueService;
            _logger = logger;
        }

        /// <summary>
        /// Get email queue statistics
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<EmailQueueStats>> GetQueueStats()
        {
            try
            {
                var stats = await _emailQueueService.GetQueueStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting email queue statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Queue a new email
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<int>> QueueEmail([FromBody] EmailQueueRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ToEmail))
                {
                    return BadRequest("ToEmail is required");
                }

                if (string.IsNullOrWhiteSpace(request.Subject))
                {
                    return BadRequest("Subject is required");
                }

                if (string.IsNullOrWhiteSpace(request.BodyText) && string.IsNullOrWhiteSpace(request.BodyHtml))
                {
                    return BadRequest("Either BodyText or BodyHtml is required");
                }

                var emailId = await _emailQueueService.QueueEmailAsync(request);
                return CreatedAtAction(nameof(GetQueueStats), new { id = emailId }, emailId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error queuing email");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Cancel a queued email
        /// </summary>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelEmail(int id)
        {
            try
            {
                var success = await _emailQueueService.CancelEmailAsync(id);
                if (success)
                {
                    return Ok(new { message = "Email cancelled successfully" });
                }
                else
                {
                    return NotFound(new { message = "Email not found or already sent" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling email {EmailId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Manually trigger cleanup of old emails
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<ActionResult> TriggerCleanup([FromQuery] int? olderThanDays = null)
        {
            try
            {
                var days = olderThanDays ?? 30;
                var deletedCount = await _emailQueueService.CleanupOldEmailsAsync(days);
                return Ok(new { message = $"Cleanup completed: {deletedCount} old emails removed", deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during manual email cleanup");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Test endpoint to queue a simple test email
        /// </summary>
        [HttpPost("test")]
        public async Task<ActionResult> QueueTestEmail([FromQuery] string? toEmail = null)
        {
            try
            {
                var testEmail = toEmail ?? "<EMAIL>";
                var emailId = await _emailQueueService.QueueEmailAsync(
                    testEmail,
                    "Test Email from Queue System",
                    "<h1>Test Email</h1><p>This is a test email sent through the email queue system.</p><p>Sent at: " + DateTime.Now + "</p>",
                    true
                );

                return Ok(new {
                    message = "Test email queued successfully",
                    emailId,
                    toEmail = testEmail,
                    note = "Email will be processed by the background service within 1 minute"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error queuing test email");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
