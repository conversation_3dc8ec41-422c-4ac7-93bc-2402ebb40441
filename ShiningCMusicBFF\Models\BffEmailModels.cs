using System.ComponentModel.DataAnnotations;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Models
{
    public class BffSendEmailRequest
    {
        [Required]
        [EmailAddress]
        public string ToEmail { get; set; } = string.Empty;

        [Required]
        public string Subject { get; set; } = string.Empty;

        [Required]
        public string Body { get; set; } = string.Empty;

        public bool IsHtml { get; set; } = true;
    }

    public class BffSendTemplateEmailRequest
    {
        [Required]
        public string TemplateName { get; set; } = string.Empty;

        // For new recipient-based approach
        public EmailRecipient? Recipient { get; set; }

        // For legacy approach (backward compatibility)
        public int? PaymentDeadlineDays { get; set; }

        public Dictionary<string, string>? AdditionalPlaceholders { get; set; }
    }

    public class BffBulkTemplateEmailRequest
    {
        [Required]
        public string TemplateName { get; set; } = string.Empty;

        [Required]
        [MinLength(1)]
        public List<EmailRecipient> Recipients { get; set; } = new();

        public Dictionary<string, string>? GlobalPlaceholders { get; set; }
    }
}
