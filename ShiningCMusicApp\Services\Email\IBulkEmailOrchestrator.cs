using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Email;

/// <summary>
/// Orchestrates bulk email operations with progress tracking and error handling
/// </summary>
public interface IBulkEmailOrchestrator
{
    /// <summary>
    /// Sends bulk emails to multiple recipients with progress tracking
    /// </summary>
    /// <typeparam name="T">The type of entity being emailed</typeparam>
    /// <param name="recipients">Collection of entities to email</param>
    /// <param name="templateName">Name of the email template to use</param>
    /// <param name="globalPlaceholders">Global placeholders applied to all emails</param>
    /// <param name="recipientBuilder">Function to convert entity to EmailRecipient</param>
    /// <param name="progress">Optional progress reporter</param>
    /// <returns>Result containing success/failure details</returns>
    Task<BulkEmailResult> SendBulkEmailAsync<T>(
        IEnumerable<T> recipients,
        string templateName,
        Dictionary<string, string> globalPlaceholders,
        Func<T, EmailRecipient> recipientBuilder,
        IProgress<EmailProgress>? progress = null
    );


}

/// <summary>
/// Result of bulk email operation
/// </summary>
public class BulkEmailResult
{
    public bool Success { get; set; }
    public int TotalAttempted { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<EmailResult> Results { get; set; } = new();
    public string? ErrorMessage { get; set; }
}



/// <summary>
/// Progress tracking for bulk email operations
/// </summary>
public class EmailProgress
{
    public int Total { get; set; }
    public int Completed { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public int PendingCount => Total - Completed;
    public double PercentComplete => Total > 0 ? (double)Completed / Total * 100 : 0;
    public string CurrentStatus { get; set; } = "";
    public List<EmailResult> Results { get; set; } = new();
}

/// <summary>
/// Result of individual email send attempt
/// </summary>
public class EmailResult
{
    public string RecipientName { get; set; } = "";
    public string Email { get; set; } = "";
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = "";
    public DateTime SentAt { get; set; }
}
