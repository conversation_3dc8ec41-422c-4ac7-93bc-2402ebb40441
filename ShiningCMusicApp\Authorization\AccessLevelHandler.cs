using Microsoft.AspNetCore.Authorization;
using ShiningCMusicCommon.Extensions;

namespace ShiningCMusicApp.Authorization
{
    public class AccessLevelHandler : AuthorizationHandler<AccessLevelRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            AccessLevelRequirement requirement)
        {
            var userAccessLevel = context.User.GetAccessLevel();
            
            if (userAccessLevel >= requirement.MinimumAccessLevel)
            {
                context.Succeed(requirement);
            }
            
            return Task.CompletedTask;
        }
    }
}
