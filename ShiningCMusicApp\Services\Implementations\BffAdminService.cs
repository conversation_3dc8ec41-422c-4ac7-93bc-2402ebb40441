using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffAdminService : IBffAdminService
    {
        private readonly IBffUsersService _usersService;
        private readonly IBffSubjectsService _subjectsService;
        private readonly IBffLocationsService _locationsService;
        private readonly IBffTutorsService _tutorsService;
        private readonly IBffStudentsService _studentsService;
        private readonly ILogger<BffAdminService> _logger;

        public BffAdminService(
            IBffUsersService usersService,
            IBffSubjectsService subjectsService,
            IBffLocationsService locationsService,
            IBffTutorsService tutorsService,
            IBffStudentsService studentsService,
            ILogger<BffAdminService> logger)
        {
            _usersService = usersService;
            _subjectsService = subjectsService;
            _locationsService = locationsService;
            _tutorsService = tutorsService;
            _studentsService = studentsService;
            _logger = logger;
        }

        public async Task<AdminData?> GetAdminDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching admin data from individual BFF services");

                // Fetch data from individual services in parallel
                var usersTask = _usersService.GetUsersAsync();
                var userRolesTask = _usersService.GetUserRolesAsync();
                var subjectsTask = _subjectsService.GetSubjectsAsync();
                var locationsTask = _locationsService.GetLocationsAsync();
                var tutorsTask = _tutorsService.GetTutorsAsync();
                var studentsTask = _studentsService.GetStudentsAsync();

                await Task.WhenAll(usersTask, userRolesTask, subjectsTask, locationsTask, tutorsTask, studentsTask);

                var adminData = new AdminData
                {
                    Users = await usersTask,
                    UserRoles = await userRolesTask,
                    Subjects = await subjectsTask,
                    Locations = await locationsTask,
                    Tutors = await tutorsTask,
                    Students = await studentsTask
                };

                _logger.LogInformation("Admin data loaded successfully. Users: {UserCount}, Subjects: {SubjectCount}, Locations: {LocationCount}",
                    adminData.Users?.Count ?? 0, adminData.Subjects?.Count ?? 0, adminData.Locations?.Count ?? 0);
                return adminData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching admin data from individual BFF services");
                return null;
            }
        }

        // User Management
        public async Task<bool> CreateUserAsync(User user)
        {
            var result = await _usersService.CreateUserAsync(user);
            return result != null;
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            return await _usersService.UpdateUserAsync(user.LoginName, user);
        }

        public async Task<bool> DeleteUserAsync(string loginName)
        {
            return await _usersService.DeleteUserAsync(loginName);
        }

        // Subject Management
        public async Task<bool> CreateSubjectAsync(Subject subject)
        {
            var result = await _subjectsService.CreateSubjectAsync(subject);
            return result != null;
        }

        public async Task<bool> UpdateSubjectAsync(Subject subject)
        {
            return await _subjectsService.UpdateSubjectAsync(subject.SubjectId, subject);
        }

        public async Task<bool> DeleteSubjectAsync(int subjectId)
        {
            return await _subjectsService.DeleteSubjectAsync(subjectId);
        }

        // Location Management
        public async Task<bool> CreateLocationAsync(Location location)
        {
            var result = await _locationsService.CreateLocationAsync(location);
            return result != null;
        }

        public async Task<bool> UpdateLocationAsync(Location location)
        {
            return await _locationsService.UpdateLocationAsync(location.LocationId, location);
        }

        public async Task<bool> DeleteLocationAsync(int locationId)
        {
            return await _locationsService.DeleteLocationAsync(locationId);
        }

        // User Role Management
        public async Task<bool> CreateUserRoleAsync(UserRole userRole)
        {
            var result = await _usersService.CreateUserRoleAsync(userRole);
            return result != null;
        }

        public async Task<bool> UpdateUserRoleAsync(UserRole userRole)
        {
            return await _usersService.UpdateUserRoleAsync(userRole.ID, userRole);
        }

        public async Task<bool> DeleteUserRoleAsync(int roleId)
        {
            return await _usersService.DeleteUserRoleAsync(roleId);
        }
    }
}
