@using ShiningCMusicApp.Services.Email
@using ShiningCMusicCommon.Models
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Buttons
@using ShiningCMusicApp.Components.Buttons
@typeparam TItem

<SfDialog @bind-Visible="IsVisible" Header="@($"Send Bulk Email to {EntityDisplayName}")" Width="1000px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogEvents OnOpen="@OnDialogOpen" OnClose="@CloseModal"></DialogEvents>
    <DialogTemplates>
        <Content>
            <div class="container-fluid">
                <!-- Template Selection -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">Email Template <span class="text-danger">*</span></label>
                        <SfDropDownList TValue="string" TItem="string" @bind-Value="selectedTemplate"
                                       DataSource="@GetTemplateNames()" 
                                       Placeholder="Select template"
                                       CssClass="form-control">
                        </SfDropDownList>
                    </div>
                </div>

                <!-- Global Placeholders -->
                <div class="row mb-3">
                    <div class="col-12">
                        <h6>Global Placeholders</h6>
                        <small class="text-muted">These values will be applied to all emails</small>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">School Name</label>
                        <input type="text" class="form-control" 
                               value="@GetGlobalPlaceholder("SchoolName")" 
                               @onchange="@((e) => SetGlobalPlaceholder("SchoolName", e.Value?.ToString() ?? ""))" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Contact Email</label>
                        <input type="text" class="form-control" 
                               value="@GetGlobalPlaceholder("ContactEmail")" 
                               @onchange="@((e) => SetGlobalPlaceholder("ContactEmail", e.Value?.ToString() ?? ""))" />
                    </div>
                </div>

                <!-- Recipients Preview -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6>Recipients (@Recipients.Count())</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary ms-1" @onclick="OnClearSelection" type="button">
                                    Clear All
                                </button>
                            </div>
                        </div>
                        
                        <div class="recipient-cards border rounded" style="max-height: 250px; overflow-y: auto; background-color: #f8f9fa;">
                            @foreach (var recipient in Recipients.Take(15))
                            {
                                <div class="card mb-1 mx-2 mt-2 border-primary">
                                    <div class="card-body py-2">
                                        <div class="row align-items-center">
                                            <div class="col-1">
                                                <input type="checkbox" 
                                                       checked="true"
                                                       @onchange="@((e) => OnToggleRecipient(recipient, e.Value?.ToString() == "true"))"
                                                       class="form-check-input" />
                                            </div>
                                            <div class="col-5">
                                                <strong>@GetDisplayName(recipient)</strong>
                                                <small class="text-muted d-block">@GetEmail(recipient)</small>
                                            </div>
                                            <div class="col-2">
                                                <span class="badge bg-info">@GetSubject(recipient)</span>
                                            </div>
                                            <div class="col-4">
                                                @if (AdditionalDisplayFields != null)
                                                {
                                                    @foreach (var field in AdditionalDisplayFields)
                                                    {
                                                        <small class="text-muted text-nowrap me-2">@field.Value(recipient)</small>
                                                    }
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            @if (Recipients.Count() > 15)
                            {
                                <div class="text-muted text-center py-2">
                                    ... and @(Recipients.Count() - 15) more recipients
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Progress Section -->
                @if (isSending)
                {
                    <EmailProgressDisplay Progress="@currentProgress" />
                }
            </div>
        </Content>
        <FooterTemplate>
            <div class="w-100">
                <div class="d-flex flex-column flex-md-row justify-content-md-between align-items-md-center">
                    <div class="text-center text-md-start mb-2 mb-md-0">
                        <small class="text-muted">@Recipients.Count() recipients selected</small>
                    </div>
                    <ModalActionButtonGroup FirstText="@($"Send to {@Recipients.Count()} Recipients")"
                                            FirstButtonClass="btn btn-success"
                                            FirstIcon="bi bi-envelope"
                                            OnFirstClick="SendBulkEmail"
                                            IsLoading="@isSending"
                                            IsFirstDisabled="@(isSending || !Recipients.Any() || string.IsNullOrEmpty(selectedTemplate))"
                                            SecondText="Cancel"
                                            IsSecondDisabled="@isSending"
                                            OnSecondClick="CloseModal" />
                </div>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

<!-- Results Modal -->
<EmailResultsModal @bind-IsVisible="showResultsModal"
                   Results="@emailResults"
                   OnClose="@HandleResultsModalClose" />
