USE [MusicSchool]
GO

/****** Object:  Table [dbo].[ClientSecrets]    Script Date: 10/05/2025 2:21:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.ClientSecrets', N'U') IS NULL
BEGIN
	CREATE TABLE [dbo].[ClientSecrets](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[ClientId] [nvarchar](100) NOT NULL,
		[PlainTextSecret] [nvarchar](500) NOT NULL,
		[CreatedAt] [datetime2](7) NOT NULL,
		[UpdatedAt] [datetime2](7) NULL,
		[Description] [nvarchar](200) NULL,
		[IsActive] [bit] NOT NULL,
		[AccessTokenLifetimeSeconds] [int] NOT NULL,
	 CONSTRAINT [PK_ClientSecrets] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
	 CONSTRAINT [UQ_ClientSecrets_ClientId] UNIQUE NONCLUSTERED 
	(
		[ClientId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
	) ON [PRIMARY]

	ALTER TABLE [dbo].[ClientSecrets] ADD  DEFAULT (getutcdate()) FOR [CreatedAt]

	ALTER TABLE [dbo].[ClientSecrets] ADD  DEFAULT ((1)) FOR [IsActive]

	ALTER TABLE [dbo].[ClientSecrets] ADD  DEFAULT ((7200)) FOR [AccessTokenLifetimeSeconds]

END
GO

-- Add ClientSecrets if they don't exist
IF NOT EXISTS (SELECT 1 FROM ClientSecrets WHERE ClientId = 'admin')
BEGIN
    INSERT INTO ClientSecrets (ClientId, PlainTextSecret, AccessTokenLifetimeSeconds)
    VALUES ('admin', 'WpujhG4r3h0K', 7200)
END

IF NOT EXISTS (SELECT 1 FROM ClientSecrets WHERE ClientId = 'bff_client')
BEGIN
    INSERT INTO ClientSecrets (ClientId, PlainTextSecret, AccessTokenLifetimeSeconds)
    VALUES ('bff_client', 'sQt8WnEvV7oC', 7200)
END
IF NOT EXISTS (SELECT 1 FROM ClientSecrets WHERE ClientId = 'wasm_client')
BEGIN
    INSERT INTO ClientSecrets (ClientId, PlainTextSecret, AccessTokenLifetimeSeconds)
    VALUES ('wasm_client', 'AE6qbzhQ08kW', 7200)
END


/****** Object:  Table [dbo].[Lessons]    Script Date: 22/06/2025 8:30:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Lessons', N'U') IS NULL
BEGIN
	CREATE TABLE [dbo].[Lessons](
		[LessonId] [int] IDENTITY(1,1) NOT NULL,
		[SubjectId] [int] NOT NULL,
		[Description] [nvarchar](500) NULL,
		[StartTime] [datetime] NOT NULL,
		[EndTime] [datetime] NOT NULL,
		[TutorId] [int] NOT NULL,
		[StudentId] [int] NOT NULL,
		[LocationId] [int] NULL,
		[IsRecurring] [bit] NOT NULL DEFAULT 0,
		[RecurrenceRule] [nvarchar](500) NULL,
		[RecurrenceException] [nvarchar](1000) NULL,
		[RecurrenceID] [int] NULL,
		[CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
		[UpdatedUTC] [datetime] NULL,
		[IsArchived] [bit] NOT NULL DEFAULT 0,
		[IsTrial] [bit] NOT NULL DEFAULT 0,
		[IsCancelled] [bit] NOT NULL DEFAULT 0,
	PRIMARY KEY CLUSTERED 
	(
		[LessonId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
	) ON [PRIMARY]
END

GO

-- Create index for better performance on recurring event queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_RecurrenceID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_RecurrenceID] ON [dbo].[Lessons]
    (
        [RecurrenceID] ASC
    ) WHERE [RecurrenceID] IS NOT NULL;
    PRINT 'Created index IX_Lessons_RecurrenceID';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_RecurrenceID already exists';
END

-- Create index for recurring events
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Lessons_IsRecurring')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Lessons_IsRecurring] ON [dbo].[Lessons]
    (
        [IsRecurring] ASC
    ) WHERE [IsRecurring] = 1;
    PRINT 'Created index IX_Lessons_IsRecurring';
END
ELSE
BEGIN
    PRINT 'Index IX_Lessons_IsRecurring already exists';
END

-- Add foreign key constraints
--ALTER TABLE [dbo].[Lessons]  WITH CHECK ADD  CONSTRAINT [FK_Lessons_TutorId] FOREIGN KEY([TutorId])
--REFERENCES [dbo].[Tutors] ([TutorId])
--GO

--ALTER TABLE [dbo].[Lessons] CHECK CONSTRAINT [FK_Lessons_TutorId]
--GO

--ALTER TABLE [dbo].[Lessons]  WITH CHECK ADD  CONSTRAINT [FK_Lessons_StudentId] FOREIGN KEY([StudentId])
--REFERENCES [dbo].[Students] ([StudentId])
--GO

--ALTER TABLE [dbo].[Lessons] CHECK CONSTRAINT [FK_Lessons_StudentId]
--GO

-- Insert some sample data
IF (SELECT COUNT(*) FROM [dbo].[Lessons]) = 0
BEGIN
	INSERT INTO [dbo].[Lessons] ([SubjectId], [StartTime], [EndTime], [TutorId], [StudentId], [LocationId])
	VALUES 
	(1, '2025-06-23 10:00:00', '2025-06-23 11:00:00', 1, 1, 1),
	(1, '2025-06-23 14:00:00', '2025-06-23 15:00:00', 1, 2, 1),
	(1, '2025-06-24 09:00:00', '2025-06-24 10:30:00', 2, 1, 2),
	(2, '2025-06-24 16:00:00', '2025-06-24 17:00:00', 1, 3, 2),
	(1, '2025-06-25 11:00:00', '2025-06-25 12:00:00', 2, 2, 1)
END
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.EmailTemplates', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[EmailTemplates](
	[Name] [nvarchar](50) NOT NULL,
	[CcEmailAddresses] [nvarchar](256) NULL,
	[BccEmailAddresses] [nvarchar](256) NULL,
	[Subject] [nvarchar](500) NULL,
	[BodyText] [nvarchar](max) NULL,
	[BodyHtml] [nvarchar](max) NULL,
 CONSTRAINT [PK_EmailTemplates] PRIMARY KEY CLUSTERED
(
	[Name] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

IF OBJECT_ID(N'dbo.EmailAttachments', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[EmailAttachments](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[TemplateName] [nvarchar](50) NOT NULL,
	[AttachmentName] [nvarchar](256) NOT NULL,
	[AttachmentPath] [nvarchar](512) NOT NULL,
 CONSTRAINT [PK_EmailAttachments] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

/****** Object:  Table [dbo].[Tutors]    Script Date: 22/06/2025 8:09:49 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Tutors', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[Tutors](
	[TutorId] [int] IDENTITY(1,1) NOT NULL,
	[TutorName] [nvarchar](50) NULL,
	[Email] [nvarchar](250) NULL,
	[SubjectId] [int] NULL,
	[LoginName] [nvarchar](20) NULL,
	[Color] [nvarchar](10) NULL,
	[CreatedUTC] [datetime] NOT NULL,
	[UpdatedUTC] [datetime] NULL,
	[IsArchived] [bit] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[TutorId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

IF NOT EXISTS (SELECT 1 FROM sys.default_constraints WHERE name = 'DF_tutors_CreatedUTC')
BEGIN
    ALTER TABLE [dbo].[Tutors] ADD  CONSTRAINT [DF_tutors_CreatedUTC]  DEFAULT (getutcdate()) FOR [CreatedUTC]
END
GO
IF NOT EXISTS (SELECT 1 FROM sys.default_constraints WHERE name = 'DF_tutors_IsArchived')
BEGIN
    ALTER TABLE [dbo].[Tutors] ADD  CONSTRAINT [DF_tutors_IsArchived]  DEFAULT ((0)) FOR [IsArchived]
END
GO

/****** Object:  Table [dbo].[Students]    Script Date: 22/06/2025 8:09:45 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Students', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[Students](
	[StudentId] [int] IDENTITY(1,1) NOT NULL,
	[StudentName] [nvarchar](50) NULL,
	[Email] [nvarchar](250) NULL,
	[SubjectId] [int] NULL,
	[TutorID] [int] NULL,
	[LoginName] [nvarchar](20) NULL,
	[CreatedUTC] [datetime] NOT NULL,
	[UpdatedUTC] [datetime] NULL,
	[IsArchived] [bit] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[StudentId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

IF NOT EXISTS (SELECT 1 FROM sys.default_constraints WHERE name = 'DF_students_CreatedUTC')
BEGIN
    ALTER TABLE [dbo].[Students] ADD  CONSTRAINT [DF_students_CreatedUTC]  DEFAULT (getutcdate()) FOR [CreatedUTC]
END
GO
IF NOT EXISTS (SELECT 1 FROM sys.default_constraints WHERE name = 'DF_students_IsArchived')
BEGIN
    ALTER TABLE [dbo].[Students] ADD  CONSTRAINT [DF_students_IsArchived]  DEFAULT ((0)) FOR [IsArchived]
END
GO

/****** Object:  Table [dbo].[Subjects]    Script Date: 24/06/2025 10:00:42 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Subjects', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[Subjects](
	[SubjectId] [int] IDENTITY(1,1) NOT NULL,
	[Subject] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED
(
	[SubjectId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

/****** Object:  Table [dbo].[Locations]    Script Date: 24/06/2025 9:45:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Locations', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[Locations](
	[LocationId] [int] IDENTITY(1,1) NOT NULL,
	[Location] [nvarchar](100) NULL,
PRIMARY KEY CLUSTERED
(
	[LocationId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[Subjects] WHERE [Subject] = 'Piano')
BEGIN
    INSERT INTO [dbo].[Subjects] ([Subject]) VALUES ('Piano')
END
IF NOT EXISTS (SELECT 1 FROM [dbo].[Subjects] WHERE [Subject] = 'Guitar')
BEGIN
    INSERT INTO [dbo].[Subjects] ([Subject]) VALUES ('Guitar')
END
IF NOT EXISTS (SELECT 1 FROM [dbo].[Subjects] WHERE [Subject] = 'Violin')
BEGIN
    INSERT INTO [dbo].[Subjects] ([Subject]) VALUES ('Violin')
END
GO


IF NOT EXISTS (SELECT 1 FROM [dbo].[Locations] WHERE [Location] = 'Room 1')
BEGIN
    INSERT INTO [dbo].[Locations] ([Location]) VALUES ('Room 1')
END
IF NOT EXISTS (SELECT 1 FROM [dbo].[Locations] WHERE [Location] = 'Room 2')
BEGIN
    INSERT INTO [dbo].[Locations] ([Location]) VALUES ('Room 2')
END
GO

/****** Object:  Table [dbo].[UserRoles]    Script Date: 25/06/2025 1:56:48 pm ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.UserRoles', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[UserRoles](
	[ID] [int] NOT NULL,
	[Description] [nvarchar](50) NULL,
 CONSTRAINT [PK_UserRoles] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 1)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([ID], [Description]) VALUES (1, 'Administrator')
END
IF NOT EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 2)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([ID], [Description]) VALUES (2, 'Tutor')
END
IF NOT EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 3)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([ID], [Description]) VALUES (3, 'Student')
END
GO

/****** Object:  Table [dbo].[Users]    Script Date: 25/06/2025 1:57:38 pm ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID(N'dbo.Users', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[Users](
	[LoginName] [nvarchar](20) NOT NULL,
	[UserName] [nvarchar](50) NULL,
	[Password] [nvarchar](100) NULL,
	[Note] [varchar](100) NULL,
	[RoleId] [int] NULL,
 CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED
(
	[LoginName] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END

GO
