using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Popups;
using ShiningCMusicApp.Services.Email;

namespace ShiningCMusicApp.Components.Email
{
    public partial class EmailResultsModal : ComponentBase
    {
        // Dialog reference for direct control
        private SfDialog dialogRef = default!;

        [Parameter] public bool IsVisible { get; set; }
        [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
        [Parameter] public BulkEmailResult? Results { get; set; }
        [Parameter] public EventCallback OnClose { get; set; }
        
        private async Task CloseModal()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await dialogRef.HideAsync();
        }
    }
}
