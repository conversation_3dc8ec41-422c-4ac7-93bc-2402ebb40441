<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
	<PackageReference Include="OpenIddict.Server.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />

    <PackageReference Include="OpenIddict.AspNetCore" Version="7.0.0" />
    <PackageReference Include="OpenIddict.EntityFrameworkCore" Version="7.0.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
	<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShiningCMusicCommon\ShiningCMusicCommon.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="identity.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>



</Project>
