using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Buttons
{
    public partial class PageActionButtonGroup : ComponentBase
    {
        // First Button Parameters
        [Parameter] public bool ShowFirstButton { get; set; } = false;
        [Parameter] public string FirstText { get; set; } = string.Empty;
        [Parameter] public string FirstTextDesktop { get; set; } = string.Empty;
        [Parameter] public string FirstTextMobile { get; set; } = string.Empty;
        [Parameter] public string FirstIcon { get; set; } = string.Empty;
        [Parameter] public string FirstIconColor { get; set; } = "white";
        [Parameter] public RenderFragment? FirstIconContent { get; set; }
        [Parameter] public string FirstButtonClass { get; set; } = "btn-success";
        [Parameter] public string FirstButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsFirstDisabled { get; set; } = false;
        [Parameter] public EventCallback OnFirstClick { get; set; }

        // Second Button Parameters
        [Parameter] public bool ShowSecondButton { get; set; } = false;
        [Parameter] public string SecondText { get; set; } = string.Empty;
        [Parameter] public string SecondTextDesktop { get; set; } = string.Empty;
        [Parameter] public string SecondTextMobile { get; set; } = string.Empty;
        [Parameter] public string SecondIcon { get; set; } = string.Empty;
        [Parameter] public string SecondIconColor { get; set; } = "white";
        [Parameter] public RenderFragment? SecondIconContent { get; set; }
        [Parameter] public string SecondButtonClass { get; set; } = "btn-info";
        [Parameter] public string SecondButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsSecondDisabled { get; set; } = false;
        [Parameter] public EventCallback OnSecondClick { get; set; }

        // Third Button Parameters (optional third button)
        [Parameter] public bool ShowThirdButton { get; set; } = true;
        [Parameter] public string ThirdText { get; set; } = string.Empty;
        [Parameter] public string ThirdTextDesktop { get; set; } = string.Empty;
        [Parameter] public string ThirdTextMobile { get; set; } = string.Empty;
        [Parameter] public string ThirdIcon { get; set; } = string.Empty;
        [Parameter] public string ThirdIconColor { get; set; } = "white";
        [Parameter] public RenderFragment? ThirdIconContent { get; set; }
        [Parameter] public string ThirdButtonClass { get; set; } = "btn-primary";
        [Parameter] public string ThirdButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsThirdDisabled { get; set; } = false;
        [Parameter] public EventCallback OnThirdClick { get; set; }

        // Fourth Button Parameters (optional fourth button)
        [Parameter] public bool ShowFourthButton { get; set; } = true;
        [Parameter] public string FourthText { get; set; } = string.Empty;
        [Parameter] public string FourthTextDesktop { get; set; } = string.Empty;
        [Parameter] public string FourthTextMobile { get; set; } = string.Empty;
        [Parameter] public string FourthIcon { get; set; } = string.Empty;
        [Parameter] public string FourthIconColor { get; set; } = "white";
        [Parameter] public RenderFragment? FourthIconContent { get; set; }
        [Parameter] public string FourthButtonClass { get; set; } = "btn-secondary";
        [Parameter] public string FourthButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsFourthDisabled { get; set; } = false;
        [Parameter] public EventCallback OnFourthClick { get; set; }

        // Container CSS Class (for customizing the container)
        [Parameter] public string ContainerClass { get; set; } = "d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end";

        private string GetFirstButtonClass()
        {
            return $"btn {FirstButtonClass}";
        }

        private string GetSecondButtonClass()
        {
            return $"btn {SecondButtonClass}";
        }

        private string GetThirdButtonClass()
        {
            return $"btn {ThirdButtonClass}";
        }

        private string GetFourthButtonClass()
        {
            return $"btn {FourthButtonClass}";
        }
    }
}
