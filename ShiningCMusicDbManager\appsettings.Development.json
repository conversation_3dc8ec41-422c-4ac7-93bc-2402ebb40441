{"ConnectionStrings": {"MusicSchool": "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;"}, "DatabaseMigration": {"BackupBeforeMigration": false, "StopOnFirstFailure": false, "VerboseLogging": true, "ConfirmBeforeApply": false}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "System": "Information", "ShiningCMusicDbManager": "Trace"}}}