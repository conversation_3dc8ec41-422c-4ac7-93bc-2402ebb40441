using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace ShiningCMusicCommon.Models
{
    [JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
    [JsonDerivedType(typeof(StudentEmailPlaceholders), "student")]
    [JsonDerivedType(typeof(TutorEmailPlaceholders), "tutor")]
    [JsonDerivedType(typeof(AdminEmailPlaceholders), "admin")]
    [JsonDerivedType(typeof(GenericEmailPlaceholders), "generic")]
    public abstract class EmailPlaceholderBase
    {
        [Required]
        [StringLength(256)]
        public string RecipientName { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [StringLength(256)]
        public string RecipientEmail { get; set; } = string.Empty;
        
        public Dictionary<string, string> CustomPlaceholders { get; set; } = new();
        
        public virtual Dictionary<string, string> ToDictionary()
        {
            var result = GetTypedPlaceholders();
            
            // Add custom placeholders (custom placeholders can override typed ones)
            foreach (var kvp in CustomPlaceholders)
            {
                result[kvp.Key] = kvp.Value;
            }
            
            return result;
        }
        
        protected abstract Dictionary<string, string> GetTypedPlaceholders();
    }

    public class StudentEmailPlaceholders : EmailPlaceholderBase
    {
        public StudentEmailPlaceholders() { }

        [Range(0, 999, ErrorMessage = "Lessons remaining must be between 0 and 999")]
        public int LessonsRemaining { get; set; }
        
        [DataType(DataType.Date)]
        public DateTime? PaymentDeadline { get; set; }
        
        [StringLength(100)]
        public string? Subject { get; set; }
        
        [Range(0.01, 10000, ErrorMessage = "Amount due must be between $0.01 and $10,000")]
        public decimal? AmountDue { get; set; }
        
        [EmailAddress]
        [StringLength(256)]
        public string? ParentEmail { get; set; }
        
        [StringLength(256)]
        public string? ParentName { get; set; }
        
        [Phone]
        public string? ParentPhone { get; set; }
        
        protected override Dictionary<string, string> GetTypedPlaceholders()
        {
            return new Dictionary<string, string>
            {
                { "RecipientName", RecipientName },
                { "RecipientEmail", RecipientEmail },
                { "StudentName", RecipientName }, // Alias for template compatibility
                { "LessonsRemaining", LessonsRemaining.ToString() },
                { "PaymentDeadline", PaymentDeadline?.ToString("MMMM dd, yyyy") ?? "" },
                { "Subject", Subject ?? "" },
                { "AmountDue", AmountDue?.ToString("C") ?? "" },
                { "ParentEmail", ParentEmail ?? "" },
                { "ParentName", ParentName ?? "" },
                { "ParentPhone", ParentPhone ?? "" }
            };
        }
    }

    public class TutorEmailPlaceholders : EmailPlaceholderBase
    {
        public TutorEmailPlaceholders() { }

        [Range(0, 999, ErrorMessage = "Active students must be between 0 and 999")]
        public int ActiveStudents { get; set; }
        
        [DataType(DataType.Date)]
        public DateTime? NextScheduleDate { get; set; }
        
        [StringLength(100)]
        public string? Department { get; set; }
        
        [StringLength(100)]
        public string? Specialization { get; set; }
        
        [Range(0, 168, ErrorMessage = "Weekly hours must be between 0 and 168")]
        public int WeeklyHours { get; set; }
        
        [DataType(DataType.Date)]
        public DateTime? LastLoginDate { get; set; }
        
        protected override Dictionary<string, string> GetTypedPlaceholders()
        {
            return new Dictionary<string, string>
            {
                { "RecipientName", RecipientName },
                { "RecipientEmail", RecipientEmail },
                { "TutorName", RecipientName }, // Alias for template compatibility
                { "ActiveStudents", ActiveStudents.ToString() },
                { "NextScheduleDate", NextScheduleDate?.ToString("MMMM dd, yyyy") ?? "" },
                { "Department", Department ?? "" },
                { "Specialization", Specialization ?? "" },
                { "WeeklyHours", WeeklyHours.ToString() },
                { "LastLoginDate", LastLoginDate?.ToString("MMMM dd, yyyy") ?? "" }
            };
        }
    }

    public class AdminEmailPlaceholders : EmailPlaceholderBase
    {
        public AdminEmailPlaceholders() { }

        [Range(0, 9999, ErrorMessage = "Total students must be between 0 and 9999")]
        public int TotalStudents { get; set; }
        
        [Range(0, 999, ErrorMessage = "Total tutors must be between 0 and 999")]
        public int TotalTutors { get; set; }
        
        [DataType(DataType.Date)]
        public DateTime? ReportDate { get; set; }
        
        [StringLength(100)]
        public string? ReportType { get; set; }
        
        [Range(0, 999999, ErrorMessage = "Total revenue must be between 0 and 999,999")]
        public decimal? TotalRevenue { get; set; }
        
        protected override Dictionary<string, string> GetTypedPlaceholders()
        {
            return new Dictionary<string, string>
            {
                { "RecipientName", RecipientName },
                { "RecipientEmail", RecipientEmail },
                { "AdminName", RecipientName }, // Alias for template compatibility
                { "TotalStudents", TotalStudents.ToString() },
                { "TotalTutors", TotalTutors.ToString() },
                { "ReportDate", ReportDate?.ToString("MMMM dd, yyyy") ?? "" },
                { "ReportType", ReportType ?? "" },
                { "TotalRevenue", TotalRevenue?.ToString("C") ?? "" }
            };
        }
    }

    public class GenericEmailPlaceholders : EmailPlaceholderBase
    {
        public GenericEmailPlaceholders() { }

        protected override Dictionary<string, string> GetTypedPlaceholders()
        {
            return new Dictionary<string, string>
            {
                { "RecipientName", RecipientName },
                { "RecipientEmail", RecipientEmail }
            };
        }
    }
}
