using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Buttons
{
    public partial class GridActionButtonGroup : ComponentBase
    {
        // First Button Parameters
        [Parameter] public bool ShowFirstButton { get; set; } = false;
        [Parameter] public string FirstText { get; set; } = string.Empty;
        [Parameter] public string FirstIcon { get; set; } = string.Empty;
        [Parameter] public string FirstButtonClass { get; set; } = "btn-outline-success";
        [Parameter] public string FirstTitle { get; set; } = string.Empty;
        [Parameter] public bool IsFirstDisabled { get; set; } = false;
        [Parameter] public EventCallback OnFirstClick { get; set; }

        // Second Button Parameters
        [Parameter] public bool ShowSecondButton { get; set; } = false;
        [Parameter] public string SecondText { get; set; } = string.Empty;
        [Parameter] public string SecondIcon { get; set; } = string.Empty;
        [Parameter] public string SecondButtonClass { get; set; } = "btn-outline-info";
        [Parameter] public string SecondTitle { get; set; } = string.Empty;
        [Parameter] public bool IsSecondDisabled { get; set; } = false;
        [Parameter] public EventCallback OnSecondClick { get; set; }

        // Third Button Parameters
        [Parameter] public bool ShowThirdButton { get; set; } = true;
        [Parameter] public string ThirdText { get; set; } = string.Empty;
        [Parameter] public string ThirdIcon { get; set; } = string.Empty; 
        [Parameter] public string ThirdButtonClass { get; set; } = "btn-outline-primary";
        [Parameter] public string ThirdTitle { get; set; } = string.Empty;
        [Parameter] public bool IsThirdDisabled { get; set; } = false;
        [Parameter] public EventCallback OnThirdClick { get; set; }

        // Fourth Button Parameters
        [Parameter] public bool ShowFourthButton { get; set; } = true;
        [Parameter] public string FourthText { get; set; } = string.Empty;
        [Parameter] public string FourthIcon { get; set; } = string.Empty;
        [Parameter] public string FourthButtonClass { get; set; } = "btn-outline-danger";
        [Parameter] public string FourthTitle { get; set; } = string.Empty;
        [Parameter] public bool IsFourthDisabled { get; set; } = false;
        [Parameter] public EventCallback OnFourthClick { get; set; }

        // General Parameters
        [Parameter] public bool ShowActionButtonLabel { get; set; } = true;
        [Parameter] public bool ShowContainer { get; set; } = true;
        [Parameter] public string ContainerClass { get; set; } = "d-flex gap-1";
        [Parameter] public string ButtonSize { get; set; } = "btn-sm";
        [Parameter] public string ButtonBaseClass { get; set; } = "grid-action-btn grid-btn-half";

        private string GetFirstButtonClass()
        {
            return $"btn {FirstButtonClass} {ButtonSize} {ButtonBaseClass}";
        }

        private string GetSecondButtonClass()
        {
            return $"btn {SecondButtonClass} {ButtonSize} {ButtonBaseClass}";
        }

        private string GetThirdButtonClass()
        {
            return $"btn {ThirdButtonClass} {ButtonSize} {ButtonBaseClass}";
        }

        private string GetFourthButtonClass()
        {
            return $"btn {FourthButtonClass} {ButtonSize} {ButtonBaseClass}";
        }
    }
}
