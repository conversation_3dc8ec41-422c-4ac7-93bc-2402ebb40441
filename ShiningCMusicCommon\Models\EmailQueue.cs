using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class EmailQueue
    {
        [Key]
        public int ID { get; set; }

        [StringLength(256)]
        public string? ToEmail { get; set; }

        [StringLength(256)]
        public string? CC { get; set; }

        [StringLength(256)]
        public string? BCC { get; set; }

        [StringLength(500)]
        public string? Subject { get; set; }

        public string? BodyText { get; set; }

        public string? BodyHtml { get; set; }

        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;

        public DateTime DeliverAfterUTC { get; set; } = DateTime.UtcNow;

        public int SentAttempts { get; set; } = 0;

        public DateTime? SentUTC { get; set; }

        public bool Cancelled { get; set; } = false;

        // Navigation properties
        public virtual ICollection<EmailQueueAttachment> Attachments { get; set; } = new List<EmailQueueAttachment>();

        // Helper properties
        public bool IsHtml => !string.IsNullOrWhiteSpace(BodyHtml);
        public string Body => IsHtml ? BodyHtml ?? "" : BodyText ?? "";
        public bool IsSent => SentUTC.HasValue;
        public bool IsReady => !Cancelled && !IsSent && DeliverAfterUTC <= DateTime.UtcNow;
    }

    public class EmailQueueRequest
    {
        [Required]
        [StringLength(256)]
        public string ToEmail { get; set; } = string.Empty;

        [StringLength(256)]
        public string? CC { get; set; }

        [StringLength(256)]
        public string? BCC { get; set; }

        [Required]
        [StringLength(500)]
        public string Subject { get; set; } = string.Empty;

        public string? BodyText { get; set; }

        public string? BodyHtml { get; set; }

        public DateTime? DeliverAfterUTC { get; set; }

        public List<EmailQueueAttachmentRequest>? Attachments { get; set; }
    }

    public class DirectEmailRequest
    {
        [Required]
        [StringLength(256)]
        public string ToEmail { get; set; } = string.Empty;

        [StringLength(256)]
        public string? CC { get; set; }

        [StringLength(256)]
        public string? BCC { get; set; }

        [Required]
        [StringLength(500)]
        public string Subject { get; set; } = string.Empty;

        public string? BodyText { get; set; }

        public string? BodyHtml { get; set; }

        public List<EmailQueueAttachmentRequest>? Attachments { get; set; }

        public bool SendImmediately { get; set; } = false;
    }
}
