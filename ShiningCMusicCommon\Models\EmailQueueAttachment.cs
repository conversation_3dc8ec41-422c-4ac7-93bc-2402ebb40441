using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class EmailQueueAttachment
    {
        [Key]
        public int ID { get; set; }

        [Required]
        public int EmailQueueId { get; set; }

        [Required]
        [StringLength(256)]
        public string AttachmentName { get; set; } = string.Empty;

        [Required]
        [StringLength(512)]
        public string AttachmentPath { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContentType { get; set; }

        public long? FileSizeBytes { get; set; }

        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual EmailQueue? EmailQueue { get; set; }
    }

    public class EmailQueueAttachmentRequest
    {
        [Required]
        [StringLength(256)]
        public string AttachmentName { get; set; } = string.Empty;

        [Required]
        [StringLength(512)]
        public string AttachmentPath { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContentType { get; set; }

        public long? FileSizeBytes { get; set; }
    }
}
