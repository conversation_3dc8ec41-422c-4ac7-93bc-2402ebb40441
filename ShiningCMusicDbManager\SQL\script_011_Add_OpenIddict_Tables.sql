-- OpenIddict Tables Migration Script (Compatible with OpenIddict 7.0.0)
-- This script creates the OpenIddict tables alongside existing IdentityServer4 tables
-- Schema matches Entity Framework Core migrations for OpenIddict 7.0.0

-- Create OpenIddict Applications table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OpenIddictApplications' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[OpenIddictApplications] (
        [Id] [nvarchar](450) NOT NULL,
        [ApplicationType] [nvarchar](50) NULL,
        [ClientId] [nvarchar](100) NULL,
        [ClientSecret] [nvarchar](max) NULL,
        [ClientType] [nvarchar](50) NULL,
        [ConcurrencyToken] [nvarchar](50) NULL,
        [ConsentType] [nvarchar](50) NULL,
        [DisplayName] [nvarchar](max) NULL,
        [DisplayNames] [nvarchar](max) NULL,
        [JsonWebKeySet] [nvarchar](max) NULL,
        [Permissions] [nvarchar](max) NULL,
        [PostLogoutRedirectUris] [nvarchar](max) NULL,
        [Properties] [nvarchar](max) NULL,
        [RedirectUris] [nvarchar](max) NULL,
        [Requirements] [nvarchar](max) NULL,
        [Settings] [nvarchar](max) NULL,
        CONSTRAINT [PK_OpenIddictApplications] PRIMARY KEY CLUSTERED ([Id] ASC)
    );

    CREATE UNIQUE NONCLUSTERED INDEX [IX_OpenIddictApplications_ClientId] ON [dbo].[OpenIddictApplications] ([ClientId] ASC) WHERE ([ClientId] IS NOT NULL);
    PRINT 'Created OpenIddictApplications table'
END

-- Create OpenIddict Authorizations table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OpenIddictAuthorizations' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[OpenIddictAuthorizations] (
        [Id] [nvarchar](450) NOT NULL,
        [ApplicationId] [nvarchar](450) NULL,
        [ConcurrencyToken] [nvarchar](50) NULL,
        [CreationDate] [datetime2](7) NULL,
        [Properties] [nvarchar](max) NULL,
        [Scopes] [nvarchar](max) NULL,
        [Status] [nvarchar](50) NULL,
        [Subject] [nvarchar](200) NULL,
        [Type] [nvarchar](50) NULL,
        CONSTRAINT [PK_OpenIddictAuthorizations] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_OpenIddictAuthorizations_OpenIddictApplications_ApplicationId] FOREIGN KEY ([ApplicationId]) REFERENCES [dbo].[OpenIddictApplications] ([Id])
    );
    
    CREATE NONCLUSTERED INDEX [IX_OpenIddictAuthorizations_ApplicationId_Status_Type] ON [dbo].[OpenIddictAuthorizations] ([ApplicationId] ASC, [Status] ASC, [Type] ASC);
    CREATE NONCLUSTERED INDEX [IX_OpenIddictAuthorizations_Subject] ON [dbo].[OpenIddictAuthorizations] ([Subject] ASC);
    PRINT 'Created OpenIddictAuthorizations table'
END

-- Create OpenIddict Scopes table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OpenIddictScopes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[OpenIddictScopes] (
        [Id] [nvarchar](450) NOT NULL,
        [ConcurrencyToken] [nvarchar](50) NULL,
        [Description] [nvarchar](max) NULL,
        [Descriptions] [nvarchar](max) NULL,
        [DisplayName] [nvarchar](max) NULL,
        [DisplayNames] [nvarchar](max) NULL,
        [Name] [nvarchar](200) NULL,
        [Properties] [nvarchar](max) NULL,
        [Resources] [nvarchar](max) NULL,
        CONSTRAINT [PK_OpenIddictScopes] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
    
    CREATE UNIQUE NONCLUSTERED INDEX [IX_OpenIddictScopes_Name] ON [dbo].[OpenIddictScopes] ([Name] ASC) WHERE ([Name] IS NOT NULL);
    PRINT 'Created OpenIddictScopes table'
END

-- Create OpenIddict Tokens table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OpenIddictTokens' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[OpenIddictTokens] (
        [Id] [nvarchar](450) NOT NULL,
        [ApplicationId] [nvarchar](450) NULL,
        [AuthorizationId] [nvarchar](450) NULL,
        [ConcurrencyToken] [nvarchar](50) NULL,
        [CreationDate] [datetime2](7) NULL,
        [ExpirationDate] [datetime2](7) NULL,
        [Payload] [nvarchar](max) NULL,
        [Properties] [nvarchar](max) NULL,
        [RedemptionDate] [datetime2](7) NULL,
        [ReferenceId] [nvarchar](100) NULL,
        [Status] [nvarchar](50) NULL,
        [Subject] [nvarchar](200) NULL,
        [Type] [nvarchar](50) NULL,
        CONSTRAINT [PK_OpenIddictTokens] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_OpenIddictTokens_OpenIddictApplications_ApplicationId] FOREIGN KEY ([ApplicationId]) REFERENCES [dbo].[OpenIddictApplications] ([Id]),
        CONSTRAINT [FK_OpenIddictTokens_OpenIddictAuthorizations_AuthorizationId] FOREIGN KEY ([AuthorizationId]) REFERENCES [dbo].[OpenIddictAuthorizations] ([Id])
    );
    
    CREATE NONCLUSTERED INDEX [IX_OpenIddictTokens_ApplicationId_Status_Type] ON [dbo].[OpenIddictTokens] ([ApplicationId] ASC, [Status] ASC, [Type] ASC);
    CREATE NONCLUSTERED INDEX [IX_OpenIddictTokens_Subject] ON [dbo].[OpenIddictTokens] ([Subject] ASC);
    CREATE NONCLUSTERED INDEX [IX_OpenIddictTokens_AuthorizationId] ON [dbo].[OpenIddictTokens] ([AuthorizationId] ASC);
    CREATE UNIQUE NONCLUSTERED INDEX [IX_OpenIddictTokens_ReferenceId] ON [dbo].[OpenIddictTokens] ([ReferenceId] ASC) WHERE ([ReferenceId] IS NOT NULL);
    PRINT 'Created OpenIddictTokens table'
END

PRINT 'OpenIddict tables created successfully!'
