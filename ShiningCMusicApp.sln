﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.36105.23
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShiningCMusicApp", "ShiningCMusicApp\ShiningCMusicApp.csproj", "{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShiningCMusicApi", "ShiningCMusicApi\ShiningCMusicApi.csproj", "{C2E046F5-08A1-4440-8836-86006D205D08}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShiningCMusicCommon", "ShiningCMusicCommon\ShiningCMusicCommon.csproj", "{F342C601-AB57-4B58-B7BB-422D7A534EDE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShiningCMusicBFF", "ShiningCMusicBFF\ShiningCMusicBFF.csproj", "{9C709E1C-00E2-48DC-924F-880AC67EDD83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShiningCMusicDbManager", "ShiningCMusicDbManager\ShiningCMusicDbManager.csproj", "{8110E4A7-437A-5DA2-1054-41916E9B7A60}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|x64.Build.0 = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Debug|x86.Build.0 = Debug|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|x64.ActiveCfg = Release|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|x64.Build.0 = Release|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|x86.ActiveCfg = Release|Any CPU
		{F7FEDD4D-BB2A-46D4-98FC-8C970C566209}.Release|x86.Build.0 = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|x64.Build.0 = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Debug|x86.Build.0 = Debug|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|x64.ActiveCfg = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|x64.Build.0 = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|x86.ActiveCfg = Release|Any CPU
		{C2E046F5-08A1-4440-8836-86006D205D08}.Release|x86.Build.0 = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|x64.Build.0 = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Debug|x86.Build.0 = Debug|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|x64.ActiveCfg = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|x64.Build.0 = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|x86.ActiveCfg = Release|Any CPU
		{F342C601-AB57-4B58-B7BB-422D7A534EDE}.Release|x86.Build.0 = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|x64.Build.0 = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Debug|x86.Build.0 = Debug|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|x64.ActiveCfg = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|x64.Build.0 = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|x86.ActiveCfg = Release|Any CPU
		{9C709E1C-00E2-48DC-924F-880AC67EDD83}.Release|x86.Build.0 = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|x64.Build.0 = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Debug|x86.Build.0 = Debug|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|Any CPU.Build.0 = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|x64.ActiveCfg = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|x64.Build.0 = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|x86.ActiveCfg = Release|Any CPU
		{8110E4A7-437A-5DA2-1054-41916E9B7A60}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {34EEDC14-E861-4671-9958-F311CCE83A52}
	EndGlobalSection
EndGlobal
