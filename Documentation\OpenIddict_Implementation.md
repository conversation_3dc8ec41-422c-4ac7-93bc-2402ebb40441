# OpenIddict Implementation Guide

## Overview

The ShiningC Music App uses OpenIddict 7.0.0 for OAuth2/OpenID Connect authentication and authorization. The implementation follows the Backend-For-Frontend (BFF) pattern with cookie-based authentication for the Blazor WebAssembly client.

## Architecture

```plaintext
Client (<PERSON><PERSON><PERSON> WASM) → BFF (<PERSON><PERSON>) → API (JWT Bearer Auth)
```

## Core Components

### 1. API Authentication Server (ShiningCMusicApi)

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddOpenIddict()
            // Configure OpenIddict core services
            .AddCore(options =>
            {
                options.UseEntityFrameworkCore()
                       .UseDbContext<ApplicationDbContext>();
            })
            
            // Configure OpenIddict server (the authorization/token endpoint)
            .AddServer(options =>
            {
                // Enable the token endpoint
                options.SetTokenEndpointUris("/connect/token");

                // Enable the password flow
                options.AllowPasswordFlow()
                       .AllowRefreshTokenFlow();

                // Accept anonymous clients (not recommended in production)
                options.AcceptAnonymousClients();

                // Register scopes
                options.RegisterScopes("api");

                // Register signing and encryption credentials
                options.AddDevelopmentEncryptionCertificate()
                       .AddDevelopmentSigningCertificate();

                // Register ASP.NET Core host and configuration
                options.UseAspNetCore()
                       .EnableTokenEndpointPassthrough();
            })

            // Register OpenIddict validation services
            .AddValidation(options =>
            {
                options.UseLocalServer();
                options.UseAspNetCore();
            });
    }
}
```

### 2. API Authorization Controller

```csharp
[ApiController]
public class AuthorizationController : Controller
{
    [HttpPost("~/connect/token")]
    [Produces("application/json")]
    public async Task<IActionResult> Exchange()
    {
        var request = HttpContext.GetOpenIddictServerRequest() ??
            throw new InvalidOperationException("The OpenID Connect request cannot be retrieved.");

        if (request.IsPasswordGrantType())
        {
            // Validate the user credentials
            var user = await _userManager.FindByNameAsync(request.Username);
            if (user == null)
            {
                return Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = OpenIddictConstants.Errors.InvalidGrant,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "Invalid credentials."
                    }));
            }

            // Validate the password
            if (!await _userManager.CheckPasswordAsync(user, request.Password))
            {
                return Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = OpenIddictConstants.Errors.InvalidGrant,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "Invalid credentials."
                    }));
            }

            // Create the claims-based identity
            var identity = new ClaimsIdentity(
                authenticationType: TokenValidationParameters.DefaultAuthenticationType,
                nameType: Claims.Name,
                roleType: Claims.Role);

            // Add claims
            identity.AddClaim(Claims.Subject, await _userManager.GetUserIdAsync(user));
            identity.AddClaim(Claims.Name, await _userManager.GetUserNameAsync(user));
            identity.AddClaim(Claims.Email, await _userManager.GetEmailAsync(user));
            identity.AddClaim("access_level", user.RoleAccessLevel.ToString());

            // Create principal & ticket
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal,
                new AuthenticationProperties(),
                OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);

            return SignIn(ticket.Principal, ticket.Properties, ticket.AuthenticationScheme);
        }

        throw new NotImplementedException("The specified grant type is not implemented.");
    }
}
```

### 3. BFF Authentication (ShiningCMusicBFF)

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Add cookie authentication
        services.AddAuthentication(options =>
        {
            options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = CookieAuthenticationDefaults.AuthenticationScheme;
        })
        .AddCookie(options =>
        {
            options.Cookie.Name = "ShiningC.Session";
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            options.Cookie.SameSite = SameSiteMode.Strict;
            options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
            options.SlidingExpiration = true;
        });

        // Configure HttpClient for API communication
        services.AddHttpClient<ApiClientService>(client =>
        {
            client.BaseAddress = new Uri(Configuration["ApiBaseUrl"]);
        })
        .AddHttpMessageHandler<JwtTokenHandler>();
    }
}
```

### 4. BFF Auth Controller

```csharp
[ApiController]
[Route("bff/auth")]
public class AuthController : ControllerBase
{
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        // Call API token endpoint
        var tokenResponse = await _apiClient.GetTokenAsync(request.Username, request.Password);
        if (!tokenResponse.IsSuccessStatusCode)
        {
            return Unauthorized();
        }

        var token = await tokenResponse.Content.ReadFromJsonAsync<TokenResponse>();

        // Store token in HttpContext for API requests
        HttpContext.Items["AccessToken"] = token.AccessToken;

        // Create claims for cookie auth
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, request.Username),
            new Claim("AccessToken", token.AccessToken)
        };

        var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var principal = new ClaimsPrincipal(identity);

        // Sign in with cookie
        await HttpContext.SignInAsync(
            CookieAuthenticationDefaults.AuthenticationScheme,
            principal,
            new AuthenticationProperties
            {
                IsPersistent = request.RememberMe,
                ExpiresUtc = DateTimeOffset.UtcNow.AddHours(12)
            });

        return Ok();
    }

    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        return Ok();
    }
}
```

### 5. JWT Token Handler

```csharp
public class JwtTokenHandler : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        // Get token from HttpContext
        var context = _contextAccessor.HttpContext;
        var token = context?.Items["AccessToken"] as string;

        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
```

## Database Schema

### OpenIddict Tables

```sql
CREATE TABLE [OpenIddictApplications] (
    [Id] nvarchar(450) NOT NULL,
    [ClientId] nvarchar(100) NULL,
    [ClientSecret] nvarchar(max) NULL,
    [ConcurrencyToken] nvarchar(50) NULL,
    [ConsentType] nvarchar(50) NULL,
    [DisplayName] nvarchar(max) NULL,
    [Type] nvarchar(50) NULL,
    CONSTRAINT [PK_OpenIddictApplications] PRIMARY KEY ([Id])
);

CREATE TABLE [OpenIddictAuthorizations] (
    [Id] nvarchar(450) NOT NULL,
    [ApplicationId] nvarchar(450) NULL,
    [ConcurrencyToken] nvarchar(50) NULL,
    [Status] nvarchar(50) NULL,
    [Subject] nvarchar(400) NULL,
    [Type] nvarchar(50) NULL,
    CONSTRAINT [PK_OpenIddictAuthorizations] PRIMARY KEY ([Id])
);

CREATE TABLE [OpenIddictTokens] (
    [Id] nvarchar(450) NOT NULL,
    [ApplicationId] nvarchar(450) NULL,
    [AuthorizationId] nvarchar(450) NULL,
    [ConcurrencyToken] nvarchar(50) NULL,
    [CreationDate] datetime2 NULL,
    [ExpirationDate] datetime2 NULL,
    [Payload] nvarchar(max) NULL,
    [Properties] nvarchar(max) NULL,
    [RedemptionDate] datetime2 NULL,
    [ReferenceId] nvarchar(100) NULL,
    [Status] nvarchar(50) NULL,
    [Subject] nvarchar(400) NULL,
    [Type] nvarchar(50) NULL,
    CONSTRAINT [PK_OpenIddictTokens] PRIMARY KEY ([Id])
);
```

## Configuration

### 1. API (appsettings.json)

```json
{
  "OpenIddict": {
    "TokenLifetime": "01:00:00",
    "RefreshTokenLifetime": "14.00:00:00",
    "RequireHttpsMetadata": true
  }
}
```

### 2. BFF (appsettings.json)

```json
{
  "Authentication": {
    "Cookie": {
      "Name": "ShiningC.Session",
      "Lifetime": "12:00:00"
    }
  },
  "Api": {
    "BaseUrl": "https://api.shiningcmusic.com"
  }
}
```

## Security Considerations

1. **Token Security**
   - Access tokens never exposed to client
   - Secure HTTP-only cookies
   - Short token lifetimes
   - Token refresh handling

2. **Cookie Security**
   - Secure flag enabled
   - HTTP-only flag enabled
   - SameSite=Strict
   - Anti-forgery protection

3. **HTTPS**
   - Required for all endpoints
   - Valid SSL certificates
   - Proper HSTS configuration

## Best Practices

1. **Token Management**
   - Implement token refresh
   - Handle token expiration
   - Secure token storage

2. **Error Handling**
   - Proper error responses
   - Security error masking
   - Logging and monitoring

3. **Configuration**
   - Use environment variables
   - Secure secrets management
   - Environment-specific settings

## Flow Diagrams

### 1. Authentication Flow

```mermaid
sequenceDiagram
    Client->>BFF: POST /bff/auth/login
    BFF->>API: POST /connect/token
    API-->>BFF: JWT Token
    BFF->>BFF: Create session cookie
    BFF-->>Client: Set-Cookie header
```

### 2. API Request Flow

```mermaid
sequenceDiagram
    Client->>BFF: Request with cookie
    BFF->>BFF: Validate cookie
    BFF->>API: Request with JWT
    API-->>BFF: Response
    BFF-->>Client: Processed response
```

## Testing

### 1. Integration Tests

```csharp
[TestClass]
public class AuthenticationTests
{
    [TestMethod]
    public async Task Login_ValidCredentials_ShouldSetAuthCookie()
    {
        // Test implementation
    }

    [TestMethod]
    public async Task ApiRequest_ValidCookie_ShouldIncludeJwt()
    {
        // Test implementation
    }
}
```

### 2. Security Tests

- HTTPS enforcement
- Cookie security
- Token validation
- CSRF protection

## Troubleshooting

1. **Common Issues**
   - Token validation failures
   - Cookie issues
   - CORS problems
   - Authentication errors

2. **Logging**
   - Token validation
   - Authentication events
   - Request/response logging

## References

- [OpenIddict Documentation](https://documentation.openiddict.com/)
- [ASP.NET Core Security](https://docs.microsoft.com/en-us/aspnet/core/security/)
- [OAuth 2.0 Specification](https://oauth.net/2/)
- [OpenID Connect](https://openid.net/connect/)
