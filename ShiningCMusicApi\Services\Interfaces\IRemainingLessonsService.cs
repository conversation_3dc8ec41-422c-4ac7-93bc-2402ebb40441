namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IRemainingLessonsService
    {
        Task<bool> UpdateStudentRemainingLessonsAsync(int studentId);
        Task<int> UpdateAllStudentsRemainingLessonsAsync();
        Task<bool> UpdateTutorRemainingLessonsAsync(int tutorId);
        Task<int> UpdateAllTutorsRemainingLessonsAsync();
        Task<int> GetRemainingLessonsForStudentByTimesheetsAsync(int studentId);
        Task<int> GetRemainingLessonsForTutorByTimesheetsAsync(int tutorId);
        Task<int> GetRemainingLessonsForStudentAsync(int studentId);
        Task<int> GetRemainingLessonsForTutorAsync(int tutorId);
        Task<IEnumerable<(int StudentId, string StudentName, string Email, int RemainingLessons)>> GetStudentsWithRemainingLessonsAsync(int lessonThreshold);
    }
}
