@echo off
REM Batch file to run the Shining C Music Database Manager

echo.
echo ========================================
echo Shining C Music Database Manager
echo ========================================
echo.

REM Check if .NET 8 is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET 8.0 runtime is not installed or not in PATH
    echo Please install .NET 8.0 runtime from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "ShiningCMusicDbManager.csproj" (
    echo ERROR: ShiningCMusicDbManager.csproj not found
    echo Please run this batch file from the ShiningCMusicDbManager directory
    pause
    exit /b 1
)

REM Check if SQL directory exists
if not exist "SQL" (
    echo WARNING: SQL directory not found
    echo Creating SQL directory...
    mkdir SQL
)

REM Run the application
echo Starting Database Manager...
echo.

if "%1"=="" (
    REM Interactive mode
    echo Running in interactive mode...
    echo Press Ctrl+C to exit at any time
    echo.
    dotnet run
) else (
    REM Command line mode
    echo Running command: %*
    echo.
    dotnet run -- %*
)

echo.
echo Database Manager finished.
pause
