/*
==============================================================================
SHINING C MUSIC - DATABASE ROLLBACK SCRIPT
==============================================================================
Script Name: RollbackAccessLevelMigration.sql
Purpose: Rollback AccessLevel column migration from UserRoles table
Version: 1.0
Date: 2025-01-09
Author: System Migration

DESCRIPTION:
This script removes the AccessLevel field from the UserRoles table and
reverts the database to its previous state before the AccessLevel migration.

WARNING: 
- This will permanently delete the AccessLevel column and all its data
- Make sure to backup the database before running this script
- Only run this if you need to completely rollback the AccessLevel feature

SAFETY FEATURES:
- Confirmation prompts
- Backup verification
- Transaction-based rollback
==============================================================================
*/

-- Enable error handling
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT 'AccessLevel Migration Rollback Script'
PRINT '====================================='
PRINT 'WARNING: This will permanently remove the AccessLevel column!'
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- Safety check - require manual confirmation
DECLARE @Confirmation VARCHAR(10) = 'NO'  -- Change to 'YES' to proceed

IF @Confirmation != 'YES'
BEGIN
    PRINT '❌ ROLLBACK CANCELLED'
    PRINT 'To proceed with rollback, change @Confirmation to ''YES'' in the script'
    PRINT 'Make sure you have a database backup before proceeding!'
    RETURN
END

-- Begin transaction for data consistency
BEGIN TRANSACTION AccessLevelRollback

BEGIN TRY
    PRINT 'Starting AccessLevel rollback...'
    
    -- Step 1: Remove the AccessLevel range constraint if it exists
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserRoles_AccessLevel_Range')
    BEGIN
        ALTER TABLE [dbo].[UserRoles]
        DROP CONSTRAINT CK_UserRoles_AccessLevel_Range
        PRINT '✓ AccessLevel range constraint removed'
    END

    -- Step 2: Remove Manager role if it was added by migration (ID = 4)
    IF EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 4 AND [Description] = 'Manager')
    BEGIN
        -- Check if any users are assigned to Manager role
        DECLARE @ManagerUsers INT
        SELECT @ManagerUsers = COUNT(*) FROM [dbo].[Users] WHERE [RoleId] = 4
        
        IF @ManagerUsers > 0
        BEGIN
            PRINT '⚠ Warning: ' + CAST(@ManagerUsers AS VARCHAR) + ' users are assigned to Manager role'
            PRINT 'These users will need to be reassigned to different roles manually'
            -- Optionally reassign to Administrator role
            -- UPDATE [dbo].[Users] SET [RoleId] = 1 WHERE [RoleId] = 4
        END
        
        DELETE FROM [dbo].[UserRoles] WHERE [ID] = 4 AND [Description] = 'Manager'
        PRINT '✓ Manager role removed'
    END

    -- Step 3: Remove AccessLevel column if it exists
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
    BEGIN
        ALTER TABLE [dbo].[UserRoles]
        DROP COLUMN [AccessLevel]
        PRINT '✓ AccessLevel column removed'
    END
    ELSE
    BEGIN
        PRINT '⚠ AccessLevel column does not exist, skipping column removal'
    END

    -- Commit transaction if all steps successful
    COMMIT TRANSACTION AccessLevelRollback
    
    PRINT ''
    PRINT '✓ Rollback completed successfully!'
    PRINT 'The UserRoles table has been reverted to its original structure'
    
END TRY
BEGIN CATCH
    -- Rollback transaction on error
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION AccessLevelRollback
    
    PRINT ''
    PRINT '✗ Rollback failed! Transaction rolled back.'
    PRINT 'Error: ' + ERROR_MESSAGE()
    PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR)
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR)
    PRINT ''
    
    -- Re-raise the error
    THROW
END CATCH

-- Final verification
PRINT ''
PRINT 'ROLLBACK SUMMARY:'
PRINT '================='
SELECT [ID], [Description] 
FROM [dbo].[UserRoles] 
ORDER BY [ID]

PRINT ''
PRINT 'Rollback completed at: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT 'Database reverted to pre-AccessLevel state'

-- Reset settings
SET NOCOUNT OFF;
