using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Dialogs
{
    public partial class RecurringEventActionDialog : ComponentBase
    {
        private Syncfusion.Blazor.Popups.SfDialog? dialogRef;
        private bool isVisible = false;
        private ActionOption selectedOption = ActionOption.EditEvent;
        private TaskCompletionSource<ActionOption?>? taskCompletionSource;
        private ActionType currentActionType = ActionType.Edit;

        public enum ActionType
        {
            Edit,
            Delete
        }

        public enum ActionOption
        {
            EditEvent,
            EditFollowingEvents,
            EditSeries,
            DeleteEvent,
            DeleteFollowingEvents,
            DeleteSeries
        }

        // Legacy enum for backward compatibility
        public enum EditOption
        {
            EditEvent,
            EditFollowingEvents,
            EditSeries
        }

        public async Task<EditOption?> ShowEditAsync()
        {
            currentActionType = ActionType.Edit;
            selectedOption = ActionOption.EditEvent;
            isVisible = true;
            taskCompletionSource = new TaskCompletionSource<ActionOption?>();
            StateHasChanged();

            var result = await taskCompletionSource.Task;

            // Convert to legacy EditOption for backward compatibility
            if (result.HasValue)
            {
                return result.Value switch
                {
                    ActionOption.EditEvent => EditOption.EditEvent,
                    ActionOption.EditFollowingEvents => EditOption.EditFollowingEvents,
                    ActionOption.EditSeries => EditOption.EditSeries,
                    _ => null
                };
            }

            return null;
        }

        public async Task<ActionOption?> ShowAsync(ActionType actionType)
        {
            currentActionType = actionType;
            selectedOption = actionType == ActionType.Edit ? ActionOption.EditEvent : ActionOption.DeleteEvent;
            isVisible = true;
            taskCompletionSource = new TaskCompletionSource<ActionOption?>();
            StateHasChanged();

            var result = await taskCompletionSource.Task;
            return result;
        }

        private void OnConfirm()
        {
            isVisible = false;
            taskCompletionSource?.SetResult(selectedOption);
            StateHasChanged();
        }

        private void OnCancel()
        {
            isVisible = false;
            taskCompletionSource?.SetResult(null);
            StateHasChanged();
        }

        private string GetHeaderIcon()
        {
            return currentActionType == ActionType.Edit ? "bi bi-arrow-repeat" : "bi bi-trash";
        }

        private string GetHeaderText()
        {
            return currentActionType == ActionType.Edit ? "Edit Recurring Event" : "Delete Recurring Event";
        }

        private string GetContentText()
        {
            return currentActionType == ActionType.Edit 
                ? "This is a recurring event. How would you like to edit it?"
                : "This is a recurring event. How would you like to delete it?";
        }
    }
}
