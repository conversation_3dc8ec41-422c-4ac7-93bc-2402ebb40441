using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Email;

/// <summary>
/// Implementation of bulk email orchestration with progress tracking
/// </summary>
public class BulkEmailOrchestrator : IBulkEmailOrchestrator
{
    private readonly IBffEmailService _emailService;

    public BulkEmailOrchestrator(IBffEmailService emailService)
    {
        _emailService = emailService;
    }

    public async Task<BulkEmailResult> SendBulkEmailAsync<T>(
        IEnumerable<T> recipients,
        string templateName,
        Dictionary<string, string> globalPlaceholders,
        Func<T, EmailRecipient> recipientBuilder,
        IProgress<EmailProgress>? progress = null)
    {
        var recipientList = recipients.ToList();
        var result = new BulkEmailResult
        {
            TotalAttempted = recipientList.Count
        };

        var emailProgress = new EmailProgress
        {
            Total = recipientList.Count,
            CurrentStatus = "Starting bulk email operation..."
        };

        progress?.Report(emailProgress);

        try
        {
            // Send emails one by one with progress tracking
            foreach (var recipient in recipientList)
            {
                var recipientName = GetRecipientDisplayName(recipient);
                emailProgress.CurrentStatus = $"Sending to {recipientName}...";
                progress?.Report(emailProgress);

                try
                {
                    // Build email recipient
                    var emailRecipient = recipientBuilder(recipient);
                    var emailRecipients = new List<EmailRecipient> { emailRecipient };

                    // Send email
                    var emailResult = await _emailService.SendBulkTemplateEmailAsync(
                        emailRecipients,
                        templateName,
                        globalPlaceholders
                    );

                    // Record result
                    var individualResult = new EmailResult
                    {
                        RecipientName = recipientName,
                        Email = emailRecipient.Email,
                        Success = emailResult?.SuccessCount > 0,
                        ErrorMessage = emailResult?.SuccessCount > 0 ? "" : "Failed to send email",
                        SentAt = DateTime.Now
                    };

                    result.Results.Add(individualResult);
                    emailProgress.Results.Add(individualResult);

                    if (individualResult.Success)
                    {
                        result.SuccessCount++;
                        emailProgress.SuccessCount++;
                    }
                    else
                    {
                        result.ErrorCount++;
                        emailProgress.ErrorCount++;
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorCount++;
                    emailProgress.ErrorCount++;

                    var errorResult = new EmailResult
                    {
                        RecipientName = recipientName,
                        Email = GetRecipientEmail(recipient),
                        Success = false,
                        ErrorMessage = ex.Message,
                        SentAt = DateTime.Now
                    };

                    result.Results.Add(errorResult);
                    emailProgress.Results.Add(errorResult);
                }

                emailProgress.Completed++;
                progress?.Report(emailProgress);

                // Small delay to prevent overwhelming the email service
                await Task.Delay(500);
            }

            emailProgress.CurrentStatus = "Bulk email operation completed";
            progress?.Report(emailProgress);

            result.Success = result.SuccessCount > 0;
            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            emailProgress.CurrentStatus = $"Error: {ex.Message}";
            progress?.Report(emailProgress);
            return result;
        }
    }

    private string GetRecipientDisplayName<T>(T recipient)
    {
        // Try to get a display name using common property names
        var type = typeof(T);
        
        var nameProperty = type.GetProperty("StudentName") ?? 
                          type.GetProperty("TutorName") ?? 
                          type.GetProperty("Name") ?? 
                          type.GetProperty("DisplayName");
        
        if (nameProperty != null)
        {
            return nameProperty.GetValue(recipient)?.ToString() ?? "Unknown";
        }

        return recipient?.ToString() ?? "Unknown";
    }

    private string GetRecipientEmail<T>(T recipient)
    {
        // Try to get email using common property names
        var type = typeof(T);
        var emailProperty = type.GetProperty("Email") ?? type.GetProperty("EmailAddress");
        
        if (emailProperty != null)
        {
            return emailProperty.GetValue(recipient)?.ToString() ?? "No Email";
        }

        return "No Email";
    }
}
