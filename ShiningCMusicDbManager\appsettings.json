{"ConnectionStrings": {"MusicSchool": "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;"}, "DatabaseMigration": {"AutoApplyMigrations": true, "SqlScriptsPath": "SQL1", "BackupBeforeMigration": false, "StopOnFirstFailure": true, "CommandTimeoutSeconds": 300, "UseTransactions": true, "BackupDirectory": "Backups", "VerboseLogging": true, "CreateBackupOnStart": false, "ValidateDependencies": true, "ShowExecutionTime": true, "ConfirmBeforeApply": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning", "ShiningCMusicDbManager": "Debug"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}}