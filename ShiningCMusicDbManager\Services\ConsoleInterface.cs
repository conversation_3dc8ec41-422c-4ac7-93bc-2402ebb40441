using Microsoft.Extensions.Logging;
using ShiningCMusicDbManager.Models;
using Spectre.Console;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ShiningCMusicDbManager.Services
{
    /// <summary>
    /// Console interface for the Database Migration Manager
    /// </summary>
    public class ConsoleInterface
    {
        private readonly IDatabaseMigrationService _migrationService;
        private readonly ILogger<ConsoleInterface> _logger;

        public ConsoleInterface(IDatabaseMigrationService migrationService, ILogger<ConsoleInterface> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task RunAsync()
        {
            // Display welcome banner
            DisplayWelcomeBanner();

            // Initialize migration system
            if (!await InitializeSystemAsync())
            {
                AnsiConsole.MarkupLine("[red]Failed to initialize migration system. Exiting.[/]");
                return;
            }

            // Main menu loop
            bool exitRequested = false;
            while (!exitRequested)
            {
                try
                {
                    var choice = ShowMainMenu();
                    exitRequested = await HandleMenuChoice(choice);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in main menu loop");
                    AnsiConsole.MarkupLine($"[red]Error: {ex.Message}[/]");
                    AnsiConsole.WriteLine("Press any key to continue...");
                    Console.ReadKey();
                }
            }

            AnsiConsole.MarkupLine("[green]Thank you for using Shining C Music Database Manager![/]");
        }

        private void DisplayWelcomeBanner()
        {
            var rule = new Rule("[yellow]Shining C Music Database Manager[/]")
            {
                Style = Style.Parse("yellow")
            };
            AnsiConsole.Write(rule);

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[cyan]Standalone database migration manager for Shining C Music application[/]");
            AnsiConsole.MarkupLine("[dim]Version 1.0.0[/]");
            AnsiConsole.WriteLine();
        }

        private async Task<bool> InitializeSystemAsync()
        {
            return await AnsiConsole.Status()
                .StartAsync("Initializing migration system...", async ctx =>
                {
                    ctx.Spinner(Spinner.Known.Star);
                    ctx.SpinnerStyle(Style.Parse("green"));

                    var success = await _migrationService.InitializeMigrationSystemAsync();
                    
                    if (success)
                    {
                        ctx.Status("System initialized successfully!");
                        AnsiConsole.MarkupLine("[green]✓ Migration system initialized successfully[/]");
                    }
                    else
                    {
                        ctx.Status("System initialization failed!");
                        AnsiConsole.MarkupLine("[red]✗ Migration system initialization failed[/]");
                    }

                    return success;
                });
        }

        private string ShowMainMenu()
        {
            AnsiConsole.WriteLine();
            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("[green]What would you like to do?[/]")
                    .PageSize(10)
                    .AddChoices(new[]
                    {
                        "View Migration Status",
                        "Apply All Migrations",
                        "Apply Specific Migration",
                        "Rollback Migration",
                        "Validate Database Connection",
                        "Validate Dependencies",
                        "Create Database Backup",
                        "Discover Scripts",
                        "Mark Migration as Applied",
                        "Remove Migration Record",
                        "Exit"
                    }));

            return choice;
        }

        private async Task<bool> HandleMenuChoice(string choice)
        {
            AnsiConsole.WriteLine();

            switch (choice)
            {
                case "View Migration Status":
                    await ShowMigrationStatusAsync();
                    break;

                case "Apply All Migrations":
                    await ApplyAllMigrationsAsync();
                    break;

                case "Apply Specific Migration":
                    await ApplySpecificMigrationAsync();
                    break;

                case "Rollback Migration":
                    await RollbackMigrationAsync();
                    break;

                case "Validate Database Connection":
                    await ValidateDatabaseConnectionAsync();
                    break;

                case "Validate Dependencies":
                    await ValidateDependenciesAsync();
                    break;

                case "Create Database Backup":
                    await CreateDatabaseBackupAsync();
                    break;

                case "Discover Scripts":
                    await DiscoverScriptsAsync();
                    break;

                case "Mark Migration as Applied":
                    await MarkMigrationAsAppliedAsync();
                    break;

                case "Remove Migration Record":
                    await RemoveMigrationRecordAsync();
                    break;

                case "Exit":
                    return true;

                default:
                    AnsiConsole.MarkupLine("[red]Invalid choice[/]");
                    break;
            }

            if (choice != "Exit")
            {
                AnsiConsole.WriteLine();
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
            }

            return false;
        }

        private async Task ShowMigrationStatusAsync()
        {
            await AnsiConsole.Status()
                .StartAsync("Loading migration status...", async ctx =>
                {
                    var statuses = await _migrationService.GetMigrationStatusAsync();

                    if (!statuses.Any())
                    {
                        AnsiConsole.MarkupLine("[yellow]No migration scripts found[/]");
                        return;
                    }

                    var table = new Table();
                    table.AddColumn("Script #");
                    table.AddColumn("Script Name");
                    table.AddColumn("Status");
                    table.AddColumn("Applied Date");
                    table.AddColumn("Changed");

                    foreach (var status in statuses)
                    {
                        var statusColor = status.Status switch
                        {
                            "Applied" => "green",
                            "Pending" => "yellow",
                            "Changed" => "orange1",
                            "Failed" => "red",
                            _ => "white"
                        };

                        var changedIcon = status.HasChanged ? "[red]⚠[/]" : "[green]✓[/]";
                        var appliedDate = status.AppliedDate?.ToString("yyyy-MM-dd HH:mm") ?? "-";

                        table.AddRow(
                            status.ScriptNumber.ToString("000"),
                            status.ScriptName,
                            $"[{statusColor}]{status.Status}[/]",
                            appliedDate,
                            changedIcon
                        );
                    }

                    AnsiConsole.Write(table);

                    // Summary
                    var applied = statuses.Count(s => s.IsApplied);
                    var pending = statuses.Count(s => !s.IsApplied);
                    var changed = statuses.Count(s => s.HasChanged);

                    AnsiConsole.WriteLine();
                    AnsiConsole.MarkupLine($"[green]Applied:[/] {applied} | [yellow]Pending:[/] {pending} | [orange1]Changed:[/] {changed}");
                });
        }

        private async Task ApplyAllMigrationsAsync()
        {
            var confirm = AnsiConsole.Confirm("[yellow]Are you sure you want to apply all pending migrations?[/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Progress()
                .StartAsync(async ctx =>
                {
                    var task = ctx.AddTask("[green]Applying migrations...[/]");
                    
                    var result = await _migrationService.ApplyAllMigrationsAsync();
                    
                    task.Value = 100;

                    DisplayMigrationResult(result);
                });
        }

        private async Task ApplySpecificMigrationAsync()
        {
            var scripts = await _migrationService.DiscoverScriptsAsync();
            if (!scripts.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No scripts found[/]");
                return;
            }

            var scriptName = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a script to apply:")
                    .PageSize(15)
                    .AddChoices(scripts.Select(s => s.Name)));

            var confirm = AnsiConsole.Confirm($"[yellow]Apply migration '{scriptName}'?[/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Status()
                .StartAsync($"Applying migration: {scriptName}...", async ctx =>
                {
                    var result = await _migrationService.ApplySpecificMigrationAsync(scriptName);
                    DisplayMigrationResult(result);
                });
        }

        private void DisplayMigrationResult(MigrationResult result)
        {
            AnsiConsole.WriteLine();
            
            if (result.Success)
            {
                AnsiConsole.MarkupLine($"[green]✓ {result.Message}[/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ {result.Message}[/]");
            }

            if (result.AppliedScripts.Any())
            {
                AnsiConsole.MarkupLine($"[green]Applied scripts ({result.AppliedScripts.Count}):[/]");
                foreach (var script in result.AppliedScripts)
                {
                    AnsiConsole.MarkupLine($"  [green]✓[/] {script}");
                }
            }

            if (result.FailedScripts.Any())
            {
                AnsiConsole.MarkupLine($"[red]Failed scripts ({result.FailedScripts.Count}):[/]");
                foreach (var script in result.FailedScripts)
                {
                    AnsiConsole.MarkupLine($"  [red]✗[/] {script}");
                }
            }

            if (result.Warnings.Any())
            {
                AnsiConsole.MarkupLine($"[yellow]Warnings ({result.Warnings.Count}):[/]");
                foreach (var warning in result.Warnings)
                {
                    AnsiConsole.MarkupLine($"  [yellow]⚠[/] {warning}");
                }
            }

            AnsiConsole.MarkupLine($"[dim]Total execution time: {result.TotalExecutionTime.TotalSeconds:F2} seconds[/]");
        }

        private async Task RollbackMigrationAsync()
        {
            var statuses = await _migrationService.GetMigrationStatusAsync();
            var appliedScripts = statuses.Where(s => s.IsApplied).Select(s => s.ScriptName).ToList();

            if (!appliedScripts.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No applied migrations to rollback[/]");
                return;
            }

            var scriptName = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a migration to rollback:")
                    .PageSize(15)
                    .AddChoices(appliedScripts));

            var confirm = AnsiConsole.Confirm($"[red]Rollback migration '{scriptName}'? This action cannot be undone![/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Status()
                .StartAsync($"Rolling back migration: {scriptName}...", async ctx =>
                {
                    var result = await _migrationService.RollbackMigrationAsync(scriptName);
                    DisplayMigrationResult(result);
                });
        }

        private async Task ValidateDatabaseConnectionAsync()
        {
            await AnsiConsole.Status()
                .StartAsync("Validating database connection...", async ctx =>
                {
                    var isValid = await _migrationService.ValidateDatabaseConnectionAsync();

                    if (isValid)
                    {
                        AnsiConsole.MarkupLine("[green]✓ Database connection is valid[/]");
                    }
                    else
                    {
                        AnsiConsole.MarkupLine("[red]✗ Database connection failed[/]");
                    }
                });
        }

        private async Task ValidateDependenciesAsync()
        {
            await AnsiConsole.Status()
                .StartAsync("Validating script dependencies...", async ctx =>
                {
                    var issues = await _migrationService.ValidateDependenciesAsync();

                    if (!issues.Any())
                    {
                        AnsiConsole.MarkupLine("[green]✓ All dependencies are satisfied[/]");
                    }
                    else
                    {
                        AnsiConsole.MarkupLine($"[red]✗ Found {issues.Count} dependency issues:[/]");
                        foreach (var issue in issues)
                        {
                            AnsiConsole.MarkupLine($"  [red]•[/] {issue}");
                        }
                    }
                });
        }

        private async Task CreateDatabaseBackupAsync()
        {
            var confirm = AnsiConsole.Confirm("[yellow]Create a database backup? This may take several minutes.[/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Status()
                .StartAsync("Creating database backup...", async ctx =>
                {
                    ctx.Spinner(Spinner.Known.Clock);

                    var backupPath = await _migrationService.CreateDatabaseBackupAsync();

                    if (backupPath != null)
                    {
                        AnsiConsole.MarkupLine($"[green]✓ Database backup created successfully[/]");
                        AnsiConsole.MarkupLine($"[dim]Location: {backupPath}[/]");
                    }
                    else
                    {
                        AnsiConsole.MarkupLine("[red]✗ Failed to create database backup[/]");
                    }
                });
        }

        private async Task DiscoverScriptsAsync()
        {
            await AnsiConsole.Status()
                .StartAsync("Discovering SQL scripts...", async ctx =>
                {
                    var scripts = await _migrationService.DiscoverScriptsAsync();

                    if (!scripts.Any())
                    {
                        AnsiConsole.MarkupLine("[yellow]No SQL scripts found[/]");
                        return;
                    }

                    var table = new Table();
                    table.AddColumn("Script #");
                    table.AddColumn("Script Name");
                    table.AddColumn("Category");
                    table.AddColumn("Size");
                    table.AddColumn("Last Modified");

                    foreach (var script in scripts)
                    {
                        var sizeKb = script.FileSize / 1024.0;
                        var sizeDisplay = sizeKb < 1 ? $"{script.FileSize} B" : $"{sizeKb:F1} KB";

                        table.AddRow(
                            script.ScriptNumber.ToString("000"),
                            script.Name,
                            script.Category.ToString(),
                            sizeDisplay,
                            script.LastModified.ToString("yyyy-MM-dd HH:mm")
                        );
                    }

                    AnsiConsole.Write(table);
                    AnsiConsole.MarkupLine($"[green]Found {scripts.Count} SQL scripts[/]");
                });
        }

        private async Task MarkMigrationAsAppliedAsync()
        {
            var scripts = await _migrationService.DiscoverScriptsAsync();
            var statuses = await _migrationService.GetMigrationStatusAsync();
            var unappliedScripts = statuses.Where(s => !s.IsApplied).Select(s => s.ScriptName).ToList();

            if (!unappliedScripts.Any())
            {
                AnsiConsole.MarkupLine("[yellow]All scripts are already marked as applied[/]");
                return;
            }

            var scriptName = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a script to mark as applied:")
                    .PageSize(15)
                    .AddChoices(unappliedScripts));

            var confirm = AnsiConsole.Confirm($"[yellow]Mark '{scriptName}' as applied without executing it?[/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Status()
                .StartAsync($"Marking migration as applied: {scriptName}...", async ctx =>
                {
                    var success = await _migrationService.MarkMigrationAsAppliedAsync(scriptName, "DatabaseManager");

                    if (success)
                    {
                        AnsiConsole.MarkupLine($"[green]✓ Marked '{scriptName}' as applied[/]");
                    }
                    else
                    {
                        AnsiConsole.MarkupLine($"[red]✗ Failed to mark '{scriptName}' as applied[/]");
                    }
                });
        }

        private async Task RemoveMigrationRecordAsync()
        {
            var statuses = await _migrationService.GetMigrationStatusAsync();
            var appliedScripts = statuses.Where(s => s.IsApplied).Select(s => s.ScriptName).ToList();

            if (!appliedScripts.Any())
            {
                AnsiConsole.MarkupLine("[yellow]No applied migrations to remove[/]");
                return;
            }

            var scriptName = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a migration record to remove:")
                    .PageSize(15)
                    .AddChoices(appliedScripts));

            var confirm = AnsiConsole.Confirm($"[red]Remove migration record for '{scriptName}'? This will allow it to be applied again![/]");
            if (!confirm)
            {
                AnsiConsole.MarkupLine("[dim]Operation cancelled[/]");
                return;
            }

            await AnsiConsole.Status()
                .StartAsync($"Removing migration record: {scriptName}...", async ctx =>
                {
                    var success = await _migrationService.RemoveMigrationRecordAsync(scriptName);

                    if (success)
                    {
                        AnsiConsole.MarkupLine($"[green]✓ Removed migration record for '{scriptName}'[/]");
                    }
                    else
                    {
                        AnsiConsole.MarkupLine($"[red]✗ Failed to remove migration record for '{scriptName}'[/]");
                    }
                });
        }
    }
}
