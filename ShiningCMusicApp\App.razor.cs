﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace ShiningCMusicApp
{
    public partial class App : ComponentBase
    {
        [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await JSRuntime.InvokeVoidAsync("preventKeyboardClose");
            }
        }
    }
}
