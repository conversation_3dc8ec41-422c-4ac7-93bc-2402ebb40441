using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class TutorService : ITutorService
    {
        private readonly string _connectionString;

        public TutorService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<Tutor>> GetTutorsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE IsArchived = 0 ORDER BY TutorId";
            return await connection.QueryAsync<Tutor>(sql);
        }

        public async Task<Tutor?> GetTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE TutorId = @Id AND IsArchived = 0";
            return await connection.QueryFirstOrDefaultAsync<Tutor>(sql, new { Id = id });
        }

        public async Task<Tutor> CreateTutorAsync(Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Tutors (TutorName, Email, SubjectId, LoginName, Color, CreatedUTC, IsArchived)
                VALUES (@TutorName, @Email, @SubjectId, @LoginName, @Color, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                tutor.TutorName,
                tutor.Email,
                tutor.SubjectId,
                tutor.LoginName,
                tutor.Color
            });

            tutor.TutorId = newId;
            return tutor;
        }

        public async Task<bool> UpdateTutorAsync(int id, Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET TutorName = @TutorName,
                    Email = @Email,
                    SubjectId = @SubjectId,
                    LoginName = @LoginName,
                    Color = @Color,
                    UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                tutor.TutorName,
                tutor.Email,
                tutor.SubjectId,
                tutor.LoginName,
                tutor.Color
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateTutorColorAsync(int tutorId, string color)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET Color = @Color, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @TutorId AND IsArchived = 0";

            var parameters = new
            {
                TutorId = tutorId,
                Color = color
            };

            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }

        public async Task<int> PermanentlyDeleteArchivedTutorsAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Tutors
                WHERE IsArchived = 1
                AND UpdatedUTC IS NOT NULL
                AND UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var rowsAffected = await connection.ExecuteAsync(sql, new { OlderThanDays = olderThanDays });
            return rowsAffected;
        }

        public async Task<int> GetArchivedTutorsCountAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT COUNT(*)
                FROM Tutors
                WHERE IsArchived = 1
                AND UpdatedUTC IS NOT NULL
                AND UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var count = await connection.QuerySingleAsync<int>(sql, new { OlderThanDays = olderThanDays });
            return count;
        }
    }
}
