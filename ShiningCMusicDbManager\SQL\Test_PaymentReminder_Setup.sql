USE [MusicSchool]
GO

-- Test script to verify Payment Reminder Service setup
PRINT 'Testing Payment Reminder Service Setup...'
PRINT ''

-- 1. Check if ExcludeEmail field exists in Students table
PRINT '1. Checking ExcludeEmail field in Students table...'
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Students]') AND name = 'ExcludeEmail')
BEGIN
    PRINT '   ✓ ExcludeEmail field exists in Students table'
END
ELSE
BEGIN
    PRINT '   ✗ ExcludeEmail field NOT found in Students table'
    PRINT '   Run: Add_ExcludeEmail_Field.sql'
END
PRINT ''

-- 2. Check if PaymentReminder email template exists
PRINT '2. Checking PaymentReminder email template...'
IF EXISTS (SELECT * FROM [dbo].[EmailTemplates] WHERE [Name] = 'PaymentReminder')
BEGIN
    PRINT '   ✓ PaymentReminder email template exists'
    
    -- Show template details
    SELECT 
        '   Template Subject: ' + ISNULL([Subject], 'NULL') as Info
    FROM [dbo].[EmailTemplates] 
    WHERE [Name] = 'PaymentReminder'
END
ELSE
BEGIN
    PRINT '   ✗ PaymentReminder email template NOT found'
    PRINT '   Run: Add_PaymentReminder_Template.sql'
END
PRINT ''

-- 3. Show sample students with their email settings
PRINT '3. Sample students and their email settings...'
IF EXISTS (SELECT * FROM Students)
BEGIN
    SELECT TOP 5
        StudentId,
        StudentName,
        Email,
        CASE WHEN ExcludeEmail = 1 THEN 'Excluded' ELSE 'Included' END as EmailStatus,
        CASE WHEN IsArchived = 1 THEN 'Archived' ELSE 'Active' END as Status
    FROM Students
    ORDER BY StudentId
END
ELSE
BEGIN
    PRINT '   No students found in database'
END
PRINT ''

-- 4. Show sample lessons for testing
PRINT '4. Sample lessons for testing lesson counting...'
IF EXISTS (SELECT * FROM Lessons WHERE IsArchived = 0)
BEGIN
    SELECT TOP 5
        l.LessonId,
        s.StudentName,
        l.StartTime,
        l.EndTime,
        CASE WHEN l.IsRecurring = 1 THEN 'Recurring' ELSE 'Regular' END as LessonType,
        l.RecurrenceRule
    FROM Lessons l
    INNER JOIN Students s ON l.StudentId = s.StudentId
    WHERE l.IsArchived = 0
    AND l.StartTime > GETDATE()
    ORDER BY l.StartTime
END
ELSE
BEGIN
    PRINT '   No future lessons found in database'
END
PRINT ''

-- 5. Test query for students who would receive reminders (threshold = 3)
PRINT '5. Students who would receive payment reminders (threshold = 3 lessons)...'
DECLARE @threshold INT = 3

-- This is a simplified version of what the service would do
SELECT 
    s.StudentId,
    s.StudentName,
    s.Email,
    s.ExcludeEmail,
    COUNT(l.LessonId) as FutureLessons
FROM Students s
LEFT JOIN Lessons l ON s.StudentId = l.StudentId 
    AND l.IsArchived = 0 
    AND l.StartTime > GETDATE()
    AND l.IsRecurring = 0  -- Only counting non-recurring for this simple test
WHERE s.IsArchived = 0 
    AND s.ExcludeEmail = 0 
    AND s.Email IS NOT NULL 
    AND s.Email != ''
GROUP BY s.StudentId, s.StudentName, s.Email, s.ExcludeEmail
HAVING COUNT(l.LessonId) <= @threshold AND COUNT(l.LessonId) > 0
ORDER BY COUNT(l.LessonId)

PRINT ''
PRINT 'Test completed. Review results above to verify setup.'
PRINT 'Note: This test only counts non-recurring lessons. The actual service'
PRINT 'will also calculate remaining occurrences for recurring lessons.'
