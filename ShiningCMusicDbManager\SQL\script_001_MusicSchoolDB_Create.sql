-- Ensure database exists; CREATE DATABASE must not run inside a user transaction
IF DB_ID('MusicSchool') IS NULL
BEGIN
    -- Create with defaults; let SQL Server choose file locations
    EXEC('CREATE DATABASE [MusicSchool]');
END
GO

USE [master]
GO

IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
BEGIN
    IF DB_ID('MusicSchool') IS NOT NULL
    BEGIN
        EXEC [MusicSchool].[dbo].[sp_fulltext_database] @action = 'enable';
    END
END
GO

-- Database option configuration (safe to re-run)
ALTER DATABASE [MusicSchool] SET ANSI_NULL_DEFAULT OFF
GO

ALTER DATABASE [MusicSchool] SET ANSI_NULLS OFF
GO

ALTER DATABASE [MusicSchool] SET ANSI_PADDING OFF
GO

ALTER DATABASE [MusicSchool] SET ANSI_WARNINGS OFF
GO

ALTER DATABASE [MusicSchool] SET ARITHABORT OFF
GO

ALTER DATABASE [MusicSchool] SET AUTO_CLOSE OFF
GO

ALTER DATABASE [MusicSchool] SET AUTO_SHRINK OFF
GO

ALTER DATABASE [MusicSchool] SET AUTO_UPDATE_STATISTICS ON
GO

ALTER DATABASE [MusicSchool] SET CURSOR_CLOSE_ON_COMMIT OFF
GO

ALTER DATABASE [MusicSchool] SET CURSOR_DEFAULT  GLOBAL
GO

ALTER DATABASE [MusicSchool] SET CONCAT_NULL_YIELDS_NULL OFF
GO

ALTER DATABASE [MusicSchool] SET NUMERIC_ROUNDABORT OFF
GO

ALTER DATABASE [MusicSchool] SET QUOTED_IDENTIFIER OFF
GO

ALTER DATABASE [MusicSchool] SET RECURSIVE_TRIGGERS OFF
GO

ALTER DATABASE [MusicSchool] SET  DISABLE_BROKER
GO

ALTER DATABASE [MusicSchool] SET AUTO_UPDATE_STATISTICS_ASYNC OFF
GO

ALTER DATABASE [MusicSchool] SET DATE_CORRELATION_OPTIMIZATION OFF
GO

ALTER DATABASE [MusicSchool] SET TRUSTWORTHY OFF
GO

ALTER DATABASE [MusicSchool] SET ALLOW_SNAPSHOT_ISOLATION OFF
GO

ALTER DATABASE [MusicSchool] SET PARAMETERIZATION SIMPLE
GO

ALTER DATABASE [MusicSchool] SET READ_COMMITTED_SNAPSHOT OFF
GO

ALTER DATABASE [MusicSchool] SET HONOR_BROKER_PRIORITY OFF
GO

ALTER DATABASE [MusicSchool] SET RECOVERY SIMPLE
GO

ALTER DATABASE [MusicSchool] SET  MULTI_USER
GO

ALTER DATABASE [MusicSchool] SET PAGE_VERIFY CHECKSUM
GO

ALTER DATABASE [MusicSchool] SET DB_CHAINING OFF
GO

ALTER DATABASE [MusicSchool] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF )
GO

ALTER DATABASE [MusicSchool] SET TARGET_RECOVERY_TIME = 60 SECONDS
GO

ALTER DATABASE [MusicSchool] SET DELAYED_DURABILITY = DISABLED
GO

ALTER DATABASE [MusicSchool] SET ACCELERATED_DATABASE_RECOVERY = OFF
GO

ALTER DATABASE [MusicSchool] SET QUERY_STORE = ON
GO

ALTER DATABASE [MusicSchool] SET  READ_WRITE
GO
