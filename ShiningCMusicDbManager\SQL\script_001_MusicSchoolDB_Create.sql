-- Create and configure MusicSchool database only if it does not already exist
IF DB_ID('MusicSchool') IS NULL
BEGIN
    PRINT 'Creating database [MusicSchool]...';

    -- Create with defaults; let SQL Server choose file locations
    EXEC('CREATE DATABASE [MusicSchool]');

    -- Enable full-text if installed
    IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
    BEGIN
        EXEC [MusicSchool].[dbo].[sp_fulltext_database] @action = 'enable';
    END

    -- Database option configuration (safe to re-run, but only runs on first create)
    ALTER DATABASE [MusicSchool] SET ANSI_NULL_DEFAULT OFF;
    ALTER DATABASE [MusicSchool] SET ANSI_NULLS OFF;
    ALTER DATABASE [MusicSchool] SET ANSI_PADDING OFF;
    ALTER DATABASE [MusicSchool] SET ANSI_WARNINGS OFF;
    ALTER DATABASE [MusicSchool] SET ARITHABORT OFF;
    ALTER DATABASE [MusicSchool] SET AUTO_CLOSE OFF;
    ALTER DATABASE [MusicSchool] SET AUTO_SHRINK OFF;
    ALTER DATABASE [MusicSchool] SET AUTO_UPDATE_STATISTICS ON;
    ALTER DATABASE [MusicSchool] SET CURSOR_CLOSE_ON_COMMIT OFF;
    ALTER DATABASE [MusicSchool] SET CURSOR_DEFAULT  GLOBAL;
    ALTER DATABASE [MusicSchool] SET CONCAT_NULL_YIELDS_NULL OFF;
    ALTER DATABASE [MusicSchool] SET NUMERIC_ROUNDABORT OFF;
    ALTER DATABASE [MusicSchool] SET QUOTED_IDENTIFIER OFF;
    ALTER DATABASE [MusicSchool] SET RECURSIVE_TRIGGERS OFF;
    ALTER DATABASE [MusicSchool] SET DISABLE_BROKER;
    ALTER DATABASE [MusicSchool] SET AUTO_UPDATE_STATISTICS_ASYNC OFF;
    ALTER DATABASE [MusicSchool] SET DATE_CORRELATION_OPTIMIZATION OFF;
    ALTER DATABASE [MusicSchool] SET TRUSTWORTHY OFF;
    ALTER DATABASE [MusicSchool] SET ALLOW_SNAPSHOT_ISOLATION OFF;
    ALTER DATABASE [MusicSchool] SET PARAMETERIZATION SIMPLE;
    ALTER DATABASE [MusicSchool] SET READ_COMMITTED_SNAPSHOT OFF;
    ALTER DATABASE [MusicSchool] SET HONOR_BROKER_PRIORITY OFF;
    ALTER DATABASE [MusicSchool] SET RECOVERY SIMPLE;
    ALTER DATABASE [MusicSchool] SET MULTI_USER;
    ALTER DATABASE [MusicSchool] SET PAGE_VERIFY CHECKSUM;
    ALTER DATABASE [MusicSchool] SET DB_CHAINING OFF;
    ALTER DATABASE [MusicSchool] SET FILESTREAM ( NON_TRANSACTED_ACCESS = OFF );
    ALTER DATABASE [MusicSchool] SET TARGET_RECOVERY_TIME = 60 SECONDS;
    ALTER DATABASE [MusicSchool] SET DELAYED_DURABILITY = DISABLED;
    ALTER DATABASE [MusicSchool] SET ACCELERATED_DATABASE_RECOVERY = OFF;
    ALTER DATABASE [MusicSchool] SET QUERY_STORE = ON;
    ALTER DATABASE [MusicSchool] SET READ_WRITE;

    PRINT 'Database [MusicSchool] created and configured.';
END
ELSE
BEGIN
    PRINT 'Database [MusicSchool] already exists. Skipping creation and configuration.';
END
GO
