using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class UserService : IUserService
    {
        private readonly string _connectionString;
        private readonly IPasswordService _passwordService;

        public UserService(IConfiguration configuration, IPasswordService passwordService)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
            _passwordService = passwordService;
        }

        public async Task<IEnumerable<User>> GetUsersAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription, ur.AccessLevel as RoleAccessLevel
                FROM Users u
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID
                ORDER BY u.LoginName";

            return await connection.QueryAsync<User>(sql);
        }

        public async Task<User?> GetUserAsync(string loginName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription, ur.AccessLevel as RoleAccessLevel
                FROM Users u
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID
                WHERE u.LoginName = @LoginName";

            return await connection.QueryFirstOrDefaultAsync<User>(sql, new { LoginName = loginName });
        }

        public async Task<User> CreateUserAsync(User user)
        {
            using var connection = new SqlConnection(_connectionString);

            // Hash the password before storing
            var hashedPassword = _passwordService.HashPassword(user.Password ?? string.Empty);

            var sql = @"
                INSERT INTO Users (LoginName, UserName, Password, Note, RoleId)
                VALUES (@LoginName, @UserName, @Password, @Note, @RoleId);";

            await connection.ExecuteAsync(sql, new
            {
                user.LoginName,
                user.UserName,
                Password = hashedPassword, // Store hashed password
                user.Note,
                user.RoleId
            });

            // Don't return the hashed password
            user.Password = null;
            return user;
        }

        public async Task<bool> UpdateUserAsync(string loginName, User user)
        {
            using var connection = new SqlConnection(_connectionString);

            string sql;
            object parameters;

            // Check if password is being updated
            if (!string.IsNullOrEmpty(user.Password))
            {
                // Hash the new password
                var hashedPassword = _passwordService.HashPassword(user.Password);

                sql = @"
                    UPDATE Users
                    SET UserName = @UserName,
                        Password = @Password,
                        Note = @Note,
                        RoleId = @RoleId
                    WHERE LoginName = @LoginName";

                parameters = new
                {
                    LoginName = loginName,
                    user.UserName,
                    Password = hashedPassword,
                    user.Note,
                    user.RoleId
                };
            }
            else
            {
                // Don't update password if it's null or empty
                sql = @"
                    UPDATE Users
                    SET UserName = @UserName,
                        Note = @Note,
                        RoleId = @RoleId
                    WHERE LoginName = @LoginName";

                parameters = new
                {
                    LoginName = loginName,
                    user.UserName,
                    user.Note,
                    user.RoleId
                };
            }

            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteUserAsync(string loginName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "DELETE FROM Users WHERE LoginName = @LoginName";
            var rowsAffected = await connection.ExecuteAsync(sql, new { LoginName = loginName });

            return rowsAffected > 0;
        }

        public async Task<User?> AuthenticateAsync(string loginName, string password)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription, ur.AccessLevel as RoleAccessLevel
                FROM Users u
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID
                WHERE u.LoginName = @LoginName";

            var user = await connection.QueryFirstOrDefaultAsync<User>(sql, new { LoginName = loginName });

            if (user != null && !string.IsNullOrEmpty(user.Password))
            {
                // Verify the password against the stored hash
                if (_passwordService.VerifyPassword(password, user.Password))
                {
                    // Don't return the password hash
                    user.Password = null;
                    return user;
                }
            }

            return null; // Authentication failed
        }

        public async Task<IEnumerable<UserRole>> GetUserRolesAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM UserRoles ORDER BY ID";
            return await connection.QueryAsync<UserRole>(sql);
        }

        public async Task<UserRole?> CreateUserRoleAsync(UserRole role)
        {
            using var connection = new SqlConnection(_connectionString);

            // Check if role ID already exists
            var existingRole = await connection.QueryFirstOrDefaultAsync<UserRole>(
                "SELECT * FROM UserRoles WHERE ID = @ID",
                new { role.ID });

            if (existingRole != null)
            {
                throw new InvalidOperationException($"A role with ID {role.ID} already exists.");
            }

            var sql = @"
                INSERT INTO UserRoles (ID, Description, AccessLevel)
                VALUES (@ID, @Description, @AccessLevel);";

            await connection.ExecuteAsync(sql, new
            {
                role.ID,
                role.Description,
                role.AccessLevel
            });

            return role;
        }

        public async Task<bool> UpdateUserRoleAsync(UserRole role)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE UserRoles
                SET Description = @Description, AccessLevel = @AccessLevel
                WHERE ID = @ID";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                role.ID,
                role.Description,
                role.AccessLevel
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteUserRoleAsync(int roleId)
        {
            using var connection = new SqlConnection(_connectionString);

            // First check if any users are using this role
            var usersWithRole = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM Users WHERE RoleId = @RoleId",
                new { RoleId = roleId });

            if (usersWithRole > 0)
            {
                throw new InvalidOperationException($"Cannot delete role. {usersWithRole} user(s) are assigned to this role.");
            }

            var sql = "DELETE FROM UserRoles WHERE ID = @RoleId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { RoleId = roleId });

            return rowsAffected > 0;
        }

        /// <summary>
        /// One-time migration method to hash existing plain text passwords
        /// This should be called once after implementing password hashing
        /// </summary>
        public async Task MigratePasswordsToHashedAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            // Get all users with passwords
            var users = await connection.QueryAsync<User>("SELECT LoginName, Password FROM Users WHERE Password IS NOT NULL AND Password != ''");

            foreach (var user in users)
            {
                if (!string.IsNullOrEmpty(user.Password))
                {
                    // Check if password is already hashed (BCrypt hashes start with $2a$, $2b$, or $2y$)
                    if (!user.Password.StartsWith("$2"))
                    {
                        var hashedPassword = _passwordService.HashPassword(user.Password);

                        await connection.ExecuteAsync(
                            "UPDATE Users SET Password = @HashedPassword WHERE LoginName = @LoginName",
                            new { HashedPassword = hashedPassword, user.LoginName });
                    }
                }
            }
        }
    }
}
