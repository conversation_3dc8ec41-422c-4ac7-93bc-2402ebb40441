# Shining C Music Database Manager

A standalone console application for managing database migrations for the Shining C Music application.

## Features

- **Interactive Console Interface**: User-friendly menu-driven interface using Spectre.Console
- **Command Line Interface**: Support for automated operations via command line arguments
- **Numeric Script Ordering**: SQL scripts are prefixed with numbers (script_001_, script_002_, etc.) for proper execution order
- **Migration Tracking**: Comprehensive tracking of applied migrations with SHA256 hash verification
- **Transaction Safety**: Each script runs in its own transaction with rollback capabilities
- **Backup Support**: Optional database backup before applying migrations
- **Dependency Validation**: Ensures scripts are applied in the correct order
- **Error Handling**: Detailed error logging and recovery options
- **Change Detection**: Detects when scripts have been modified since last application

## Quick Start

### Prerequisites

- .NET 8.0 Runtime
- SQL Server database
- Appropriate database connection permissions

### Configuration

1. Update `appsettings.json` with your database connection string:

```json
{
  "ConnectionStrings": {
    "MusicSchool": "your-connection-string-here"
  }
}
```

2. Configure migration options in `appsettings.json`:

```json
{
  "DatabaseMigration": {
    "BackupBeforeMigration": true,
    "StopOnFirstFailure": true,
    "CommandTimeoutSeconds": 300,
    "VerboseLogging": true,
    "ConfirmBeforeApply": true
  }
}
```

### Usage

#### Interactive Mode

Run without arguments to start the interactive console:

```bash
ShiningCMusicDbManager.exe
```

This will present a menu with options to:
- View migration status
- Apply all migrations
- Apply specific migrations
- Rollback migrations
- Validate database connection
- Create database backups
- And more...

#### Command Line Mode

Run with specific commands for automation:

```bash
# Initialize the migration system
ShiningCMusicDbManager.exe init

# Show migration status
ShiningCMusicDbManager.exe status

# Apply all pending migrations
ShiningCMusicDbManager.exe apply

# Apply a specific migration
ShiningCMusicDbManager.exe apply script_001_MusicSchoolDB_Create.sql

# Rollback a migration
ShiningCMusicDbManager.exe rollback script_001_MusicSchoolDB_Create.sql

# Validate database connection
ShiningCMusicDbManager.exe validate

# Create database backup
ShiningCMusicDbManager.exe backup

# Discover available scripts
ShiningCMusicDbManager.exe discover

# Show help
ShiningCMusicDbManager.exe help
```

## SQL Script Naming Convention

SQL scripts should follow the naming convention: `script_XXX_description.sql`

- `XXX` is a 3-digit number (001, 002, etc.)
- Scripts are executed in numeric order
- Lower numbers execute first

### Script Categories

- **001-010**: Database creation and core objects
- **011-020**: Authentication and security
- **021-050**: Core application tables
- **051-100**: Field additions and modifications
- **101-150**: Updates and modifications
- **151-200**: Configuration additions
- **201-250**: Stored procedures
- **251-300**: Access level migrations
- **800-899**: Unclassified scripts
- **900+**: Sample data (always last)

## Migration Tracking

The system creates a `DatabaseMigrations` table to track:
- Script name and execution order
- Applied date and execution time
- SHA256 hash for change detection
- Success/failure status
- Error messages
- Rollback information

## Safety Features

- **Transaction Safety**: Each script runs in its own transaction
- **Change Detection**: SHA256 hashing prevents accidental re-application of modified scripts
- **Backup Support**: Optional database backup before migrations
- **Dependency Validation**: Ensures scripts are applied in correct order
- **Error Handling**: Detailed error logging and recovery options
- **Rollback Support**: Can rollback individual migrations (if rollback scripts exist)

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `BackupBeforeMigration` | Create backup before applying migrations | `true` |
| `StopOnFirstFailure` | Stop migration process on first failure | `true` |
| `CommandTimeoutSeconds` | SQL command timeout | `300` |
| `UseTransactions` | Use transactions for safety | `true` |
| `BackupDirectory` | Directory for database backups | `Backups` |
| `VerboseLogging` | Enable detailed logging | `true` |
| `ValidateDependencies` | Validate script dependencies | `true` |
| `ShowExecutionTime` | Show execution times | `true` |
| `ConfirmBeforeApply` | Confirm before applying migrations | `true` |

## Troubleshooting

### Common Issues

**Connection Timeout**
- Increase `CommandTimeoutSeconds` in configuration
- Check network connectivity to database server

**Migration Marked as Failed but Actually Succeeded**
- Use the interactive mode to mark the migration as applied
- Check the `DatabaseMigrations` table for actual status

**Script Not Found**
- Ensure the script exists in the `SQL` directory
- Check the script name spelling and case sensitivity

**Dependency Validation Fails**
- Review script execution order
- Ensure dependent scripts are applied first

### Getting Help

1. Run `ShiningCMusicDbManager.exe help` for command line options
2. Use interactive mode for guided operations
3. Check logs for detailed error messages
4. Review the `DatabaseMigrations` table for migration history

## Development

### Building

```bash
dotnet build
```

### Running in Development

```bash
dotnet run
```

### Publishing

```bash
dotnet publish -c Release -r win-x64 --self-contained
```

## Architecture

- **Models**: Data models for migrations, scripts, and results
- **Services**: Core migration service and console interface
- **Configuration**: JSON-based configuration with environment support
- **Logging**: Structured logging with console output
- **Error Handling**: Comprehensive exception handling and recovery

## License

This is part of the Shining C Music application suite.

## Version History

- **1.0.0**: Initial release with interactive and command line interfaces
  - Numeric script ordering
  - Migration tracking
  - Transaction safety
  - Backup support
  - Change detection
