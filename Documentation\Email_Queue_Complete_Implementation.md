# Email Queue System - Complete Implementation Guide

## Overview

The Email Queue System provides asynchronous email processing for the Shining C Music School application with comprehensive attachment support and flexible delivery options. Instead of sending emails immediately, emails are queued in the database and processed by a background service, improving application performance and reliability.

## ✅ COMPLETE IMPLEMENTATION

### **Core Components:**

1. **Database Tables**: `EmailQueue` and `EmailQueueAttachments` with proper indexing
2. **Models**: Complete email queue models with attachment support
3. **Services**: `EmailQueueService` for queue management and attachment handling
4. **Background Service**: `EmailProcessorService` for processing queued emails
5. **Configuration**: Database-driven configuration for processor settings (GroupId: 200)
6. **API Controllers**: Management endpoints for queue operations and direct sending
7. **Updated EmailService**: Now queues emails instead of sending directly

### **Advanced Features:**

1. **Email Attachments**: Queue emails with file attachments
2. **Direct Email Sending**: Send emails immediately bypassing the queue
3. **Flexible Email Options**: Choose between queued and immediate delivery
4. **Transaction Safety**: Atomic email + attachment operations
5. **Settings Integration**: EmailProcessor included in Settings page background processors

## Database Schema

### EmailQueue Table
```sql
CREATE TABLE [dbo].[EmailQueue](
    [ID] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [ToEmail] [nvarchar](256) NULL,
    [CC] [nvarchar](256) NULL,
    [BCC] [nvarchar](256) NOT NULL DEFAULT '',
    [Subject] [nvarchar](500) NULL,
    [BodyText] [nvarchar](max) NULL,
    [BodyHtml] [nvarchar](max) NULL,
    [CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
    [DeliverAfterUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
    [SentAttempts] [int] NOT NULL DEFAULT 0,
    [SentUTC] [datetime] NULL,
    [Cancelled] [bit] NOT NULL DEFAULT 0
)
```

### EmailQueueAttachments Table
```sql
CREATE TABLE [dbo].[EmailQueueAttachments](
    [ID] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [EmailQueueId] [int] NOT NULL,
    [AttachmentName] [nvarchar](256) NOT NULL,
    [AttachmentPath] [nvarchar](512) NOT NULL,
    [ContentType] [nvarchar](100) NULL,
    [FileSizeBytes] [bigint] NULL,
    [CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [FK_EmailQueueAttachments_EmailQueue] FOREIGN KEY([EmailQueueId])
        REFERENCES [EmailQueue] ([ID]) ON DELETE CASCADE
)
```

### Configuration Settings (GroupId: 200)
- **EmailProcessorEnabled**: Enable/disable the email processor (default: true)
- **EmailProcessorFrequencyMinutes**: Processing frequency in minutes (default: 1)
- **EmailProcessorMaxAttempts**: Maximum send attempts before giving up (default: 5)
- **EmailProcessorCleanupDays**: Days to keep sent emails before cleanup (default: 30)

## How It Works

### 1. Email Queuing Process
```csharp
// Basic email queuing
var emailId = await _emailQueueService.QueueEmailAsync(
    toEmail: "<EMAIL>",
    subject: "Lesson Reminder",
    body: "<h1>Your lesson is tomorrow</h1>",
    isHtml: true
);

// Email with attachments
var emailId = await _emailQueueService.QueueEmailAsync(
    toEmail: "<EMAIL>",
    subject: "Welcome Package",
    body: "<h1>Welcome!</h1>",
    isHtml: true,
    attachments: new List<EmailQueueAttachmentRequest>
    {
        new EmailQueueAttachmentRequest
        {
            AttachmentName = "handbook.pdf",
            AttachmentPath = "/files/handbook.pdf",
            ContentType = "application/pdf"
        }
    }
);
```

### 2. Background Processing
The `EmailProcessorService` runs continuously and:
- Checks configuration every cycle to see if processing is enabled
- Retrieves pending emails from the queue
- Loads attachments for each email
- Attempts to send each email via SMTP with attachments
- Updates the queue with success/failure status
- Performs cleanup of old sent emails
- Logs comprehensive statistics

### 3. Direct Email Sending
For urgent emails that need immediate delivery:
```csharp
// Send immediately via API endpoint POST /api/email/send-direct
var request = new DirectEmailRequest
{
    ToEmail = "<EMAIL>",
    Subject = "Urgent Notice",
    BodyHtml = "<h1>Important Update</h1>",
    SendImmediately = true,
    Attachments = attachmentList
};
```

### 4. Retry Logic & Error Handling
- Failed emails are automatically retried up to the configured maximum attempts
- Each attempt increments the `SentAttempts` counter
- Missing attachment files are logged as warnings, email still sends
- Emails exceeding max attempts are considered failed but remain in queue for manual review

## API Endpoints

### Core Queue Management

#### `GET /api/emailqueue/stats`
Returns comprehensive email queue statistics:
```json
{
    "pendingCount": 5,
    "sentTodayCount": 23,
    "failedCount": 2,
    "cancelledCount": 0
}
```

#### `POST /api/emailqueue`
Queue email with optional attachments:
```json
{
    "toEmail": "<EMAIL>",
    "subject": "Subject",
    "bodyHtml": "<h1>Content</h1>",
    "attachments": [
        {
            "attachmentName": "file.pdf",
            "attachmentPath": "/path/to/file.pdf",
            "contentType": "application/pdf",
            "fileSizeBytes": 1024000
        }
    ]
}
```

#### `POST /api/emailqueue/{id}/cancel`
Cancel a queued email

#### `POST /api/emailqueue/cleanup?olderThanDays=30`
Manually trigger cleanup

#### `POST /api/emailqueue/test?toEmail=<EMAIL>`
Queue a test email

### Direct Email Sending

#### `POST /api/email/send-direct`
Send email immediately or queue with attachments:

**Parameters:**
- `sendImmediately`: `true` = send via SMTP immediately, `false` = queue for background processing
- `attachments`: Array of attachment objects
- Standard email fields (to, subject, body, etc.)

**Request Example:**
```json
{
    "toEmail": "<EMAIL>",
    "subject": "Email Subject",
    "bodyHtml": "<h1>HTML body</h1>",
    "sendImmediately": true,
    "attachments": [
        {
            "attachmentName": "document.pdf",
            "attachmentPath": "/files/documents/document.pdf",
            "contentType": "application/pdf",
            "fileSizeBytes": 1024000
        }
    ]
}
```

**Response for Immediate Send:**
```json
{
    "message": "Email sent immediately",
    "method": "direct"
}
```

**Response for Queued Send:**
```json
{
    "message": "Email queued successfully",
    "emailId": 123,
    "method": "queued",
    "note": "Email will be processed by the background service"
}
```

## Configuration Management

### Settings Page Integration
EmailProcessor is now included in the Settings page as a background processor:
- **Toggle**: Enable/disable email processing
- **Configuration**: Frequency, max attempts, and cleanup settings
- **Real-time Control**: Changes take effect immediately

### Configuration Access Methods
1. **Via Settings UI**: Background Processors section
2. **Via Database**: Update the `Config` table directly
3. **Via API**: Use configuration endpoints to update settings

## Usage Examples

### Template Emails (Existing Code Unchanged)
```csharp
// This code remains the same - now automatically queued with attachment support
await _emailService.SendEmailFromTemplateAsync("PaymentReminder", student.Email, placeholders);
```

### Custom Emails (Existing Code Unchanged)
```csharp
// This code remains the same - now automatically queued
await _emailService.SendEmailAsync("<EMAIL>", "Subject", "Body", true);
```

### Queue Email with Multiple Attachments
```csharp
var request = new EmailQueueRequest
{
    ToEmail = "<EMAIL>",
    Subject = "Welcome Package",
    BodyHtml = "<h1>Welcome!</h1><p>Please find attached documents.</p>",
    Attachments = new List<EmailQueueAttachmentRequest>
    {
        new EmailQueueAttachmentRequest
        {
            AttachmentName = "Student_Handbook.pdf",
            AttachmentPath = "/files/handbooks/student_handbook.pdf",
            ContentType = "application/pdf",
            FileSizeBytes = 2048000
        },
        new EmailQueueAttachmentRequest
        {
            AttachmentName = "Schedule.pdf",
            AttachmentPath = "/files/schedules/schedule.pdf",
            ContentType = "application/pdf"
        }
    }
};

var emailId = await emailQueueService.QueueEmailAsync(request);
```

### Monitor Queue Status
```csharp
var stats = await _emailQueueService.GetQueueStatsAsync();
Console.WriteLine($"Pending emails: {stats.PendingCount}");
Console.WriteLine($"Failed emails: {stats.FailedCount}");
```

## Technical Implementation Details

### Transaction Safety
Email queuing with attachments uses database transactions:
```csharp
using var connection = new SqlConnection(_connectionString);
await connection.OpenAsync();
using var transaction = connection.BeginTransaction();

try
{
    // Insert email record
    var emailId = await connection.QuerySingleAsync<int>(emailSql, emailData, transaction);
    
    // Insert attachments
    foreach (var attachment in attachments)
    {
        await connection.ExecuteAsync(attachmentSql, attachmentData, transaction);
    }
    
    transaction.Commit();
}
catch
{
    transaction.Rollback();
    throw;
}
```

### Attachment Processing
1. **Queue Phase**: Attachments stored in `EmailQueueAttachments` table with file paths
2. **Load Phase**: Background service loads attachments for each email
3. **Validation Phase**: File existence checked before sending
4. **SMTP Phase**: Files attached to `MailMessage` with proper content types
5. **Error Handling**: Missing files logged as warnings, email still sends

### Background Service Flow
1. Check if EmailProcessor is enabled in configuration
2. Get processing frequency and limits from configuration
3. Perform cleanup of old sent emails
4. Retrieve pending emails from queue
5. For each email:
   - Load attachments
   - Increment attempt count
   - Send via SMTP with attachments
   - Mark as sent or log failure
6. Log comprehensive statistics
7. Wait for configured frequency before next cycle

## File Management Best Practices

### Recommended Directory Structure
```
/files/
├── handbooks/
│   ├── student_handbook.pdf
│   └── tutor_handbook.pdf
├── schedules/
│   ├── monthly_schedule.pdf
│   └── practice_schedule.pdf
├── policies/
│   └── studio_policies.pdf
└── temp/
    └── generated_reports/
```

### Security Considerations
1. **File Paths**: Use absolute paths or paths relative to a configured base directory
2. **File Validation**: Check file existence and validate file types before queuing
3. **Size Limits**: Consider implementing file size limits (e.g., 25MB total per email)
4. **Path Validation**: Prevent directory traversal attacks
5. **Virus Scanning**: Consider scanning uploaded attachments for malware

## Performance Considerations

### Attachment Handling
- Large attachments may impact email sending performance
- Background service processes attachments sequentially
- Consider implementing attachment size limits
- File validation occurs during sending, not queuing

### Direct Send vs Queue
- **Direct Send**: Immediate delivery but blocks API response
- **Queue Send**: Fast API response but delayed delivery
- **Recommendation**: Use direct send for urgent emails only

## Benefits

### Performance Improvements
- ✅ Non-blocking email operations
- ✅ Faster API response times
- ✅ Better user experience
- ✅ Handles large attachments efficiently

### Reliability
- ✅ Automatic retry mechanism
- ✅ Failure tracking and logging
- ✅ Email delivery guarantees
- ✅ Transaction safety for attachments

### Scalability
- ✅ Handles high email volumes
- ✅ Configurable processing frequency
- ✅ Database-driven queue management
- ✅ Attachment support without performance impact

### Monitoring & Management
- ✅ Comprehensive logging
- ✅ Queue statistics
- ✅ Failed email tracking
- ✅ Settings page integration
- ✅ Manual cleanup capabilities

## Troubleshooting

### Common Issues

1. **Emails Not Being Sent**
   - Check if `EmailProcessorEnabled` is set to `true` in Settings
   - Verify SMTP configuration in environment variables
   - Check application logs for error messages

2. **High Failed Email Count**
   - Review SMTP server connectivity
   - Check email addresses for validity
   - Verify authentication credentials
   - Check attachment file paths

3. **Queue Growing Too Large**
   - Increase processing frequency in Settings
   - Check for SMTP server issues
   - Review failed email logs
   - Verify attachment files exist

4. **Attachment Issues**
   - Verify file paths are correct and accessible
   - Check file permissions
   - Ensure files exist at specified paths
   - Review file size limits

### Monitoring Commands

```sql
-- Check queue status with attachments
SELECT 
    COUNT(CASE WHEN SentUTC IS NULL AND Cancelled = 0 THEN 1 END) as Pending,
    COUNT(CASE WHEN SentUTC IS NOT NULL THEN 1 END) as Sent,
    COUNT(CASE WHEN SentAttempts >= 5 AND SentUTC IS NULL THEN 1 END) as Failed,
    COUNT(DISTINCT a.EmailQueueId) as EmailsWithAttachments
FROM EmailQueue e
LEFT JOIN EmailQueueAttachments a ON e.ID = a.EmailQueueId;

-- View recent failed emails with attachment info
SELECT TOP 10 
    e.*,
    COUNT(a.ID) as AttachmentCount
FROM EmailQueue e
LEFT JOIN EmailQueueAttachments a ON e.ID = a.EmailQueueId
WHERE e.SentAttempts >= 5 AND e.SentUTC IS NULL 
GROUP BY e.ID, e.ToEmail, e.Subject, e.CreatedUTC, e.SentAttempts
ORDER BY e.CreatedUTC DESC;
```

## Migration Notes

### Backward Compatibility
- ✅ All existing email code continues to work unchanged
- ✅ Same API methods, now with queuing and attachment support
- ✅ No breaking changes to existing functionality
- ✅ ToName field removed for simplicity (non-breaking change)

### Deployment Steps
1. Run database scripts:
   - `Add_EmailQueue_And_Config.sql`
   - `Add_EmailQueueAttachments.sql`
2. Deploy updated API and client code
3. Verify EmailProcessor is enabled in Settings page
4. Test with `/api/emailqueue/test` endpoint
5. Test attachment functionality with `/api/email/send-direct`

### Code Compatibility
- ✅ All existing email code works unchanged
- ✅ New attachment features are opt-in
- ✅ Direct send functionality is additive
- ✅ Settings page automatically includes EmailProcessor

The complete email queue system now provides enterprise-grade functionality with comprehensive attachment support, flexible delivery options, and seamless integration with the existing application architecture while maintaining full backward compatibility.
