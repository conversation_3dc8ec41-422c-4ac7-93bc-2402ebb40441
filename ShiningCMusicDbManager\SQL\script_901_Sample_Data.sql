USE [MusicSchool]
GO

IF (SELECT COUNT(*) FROM [dbo].[Tutors]) = 0
BEGIN
	-- Insert sample tutors
	INSERT INTO [dbo].[Tutors] ([<PERSON>torName], [Email], [SubjectId], [LoginName], [Color])
	VALUES 
	('<PERSON>', '<EMAIL>', 3, 'sue', '#FF6B6B'),
	('<PERSON><PERSON><PERSON>', '<EMAIL>', 1, 'koeun', '#4ECDC4'),
	('<PERSON><PERSON><PERSON>', '<EMAIL>', 1, 'kathrin', '#5742f5'),
	('<PERSON>fellow', '<EMAIL>', 2, 'mitchell', '#1e88e5'),
	('<PERSON>', '<EMAIL>', 1, 'christy', '#ab47bc')
END

GO

IF (SELECT COUNT(*) FROM [dbo].[Students]) = 0
BEGIN
	-- Insert sample students
	INSERT INTO [dbo].[Students] ([<PERSON>Name], [<PERSON>ail], [SubjectId], [TutorID])
	VALUES 
	('<PERSON>', '<EMAIL>', 1, 1),
	('<PERSON> <PERSON>', '<EMAIL>', 1, 1),
	('<PERSON> <PERSON>', '<EMAIL>', 2, 2),
	('<PERSON> Wilson', '<EMAIL>', 1, 2),
	('<PERSON>', '<EMAIL>', 3, 3)
END

GO

IF (SELECT COUNT(*) FROM [dbo].[Users]) = 0
BEGIN
	-- Insert sample users
	INSERT INTO [dbo].[Users] ([LoginName], [UserName], [Password], [RoleId])
	VALUES 
	('sue', 'Sue', 'password123', 2),
	('koeun', 'Koeun', 'password123', 2),
	('kathrin', 'Kathrin', 'password123', 2),
	('mitchell', 'Mitchell', 'password123', 2),
	('christy', 'Christy', 'password123', 1),
	('admin', 'Admin', 'admin', 1)
END

GO
