/*
==============================================================================
SHINING C MUSIC - DATABASE VERIFICATION SCRIPT
==============================================================================
Script Name: VerifyAccessLevelMigration.sql
Purpose: Verify AccessLevel migration status and data integrity
Version: 1.0
Date: 2025-01-09
Author: System Migration

DESCRIPTION:
This script checks the current state of the AccessLevel migration and
validates that all data is correctly configured.

USAGE:
Run this script to verify:
- AccessLevel column exists
- All roles have valid access levels
- Constraints are properly applied
- User assignments are correct
==============================================================================
*/

SET NOCOUNT ON;

PRINT 'AccessLevel Migration Verification'
PRINT '=================================='
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- Check 1: Verify AccessLevel column exists
PRINT '1. COLUMN EXISTENCE CHECK:'
PRINT '--------------------------'
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    PRINT '✓ AccessLevel column exists in UserRoles table'
    
    -- Get column details
    SELECT 
        c.name AS ColumnName,
        t.name AS DataType,
        c.max_length,
        c.is_nullable,
        dc.definition AS DefaultValue
    FROM sys.columns c
    INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
    LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
    WHERE c.object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND c.name = 'AccessLevel'
END
ELSE
BEGIN
    PRINT '❌ AccessLevel column does NOT exist in UserRoles table'
    PRINT 'Migration has not been applied yet'
END

PRINT ''

-- Check 2: Verify constraint exists
PRINT '2. CONSTRAINT CHECK:'
PRINT '--------------------'
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserRoles_AccessLevel_Range')
BEGIN
    PRINT '✓ AccessLevel range constraint exists'
    SELECT 
        name AS ConstraintName,
        definition AS ConstraintDefinition
    FROM sys.check_constraints 
    WHERE name = 'CK_UserRoles_AccessLevel_Range'
END
ELSE
BEGIN
    PRINT '⚠ AccessLevel range constraint does NOT exist'
END

PRINT ''

-- Check 3: Display current UserRoles data
PRINT '3. CURRENT USER ROLES:'
PRINT '----------------------'
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    SELECT 
        [ID],
        [Description],
        [AccessLevel],
        CASE 
            WHEN [AccessLevel] = 100 THEN 'Administrator (Full Access)'
            WHEN [AccessLevel] = 80 THEN 'Manager (Management Access)'
            WHEN [AccessLevel] = 20 THEN 'Tutor (Teaching Access)'
            WHEN [AccessLevel] = 10 THEN 'Student (Basic Access)'
            WHEN [AccessLevel] BETWEEN 1 AND 9 THEN 'Limited Access'
            WHEN [AccessLevel] BETWEEN 21 AND 79 THEN 'Custom Access'
            WHEN [AccessLevel] BETWEEN 81 AND 99 THEN 'Senior Management'
            ELSE 'Invalid Access Level'
        END AS [Access_Description]
    FROM [dbo].[UserRoles] 
    ORDER BY [AccessLevel] DESC, [ID]
END
ELSE
BEGIN
    SELECT [ID], [Description] 
    FROM [dbo].[UserRoles] 
    ORDER BY [ID]
END

PRINT ''

-- Check 4: Validate access level ranges
PRINT '4. ACCESS LEVEL VALIDATION:'
PRINT '---------------------------'
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    DECLARE @InvalidRoles INT, @TotalRoles INT
    
    SELECT @TotalRoles = COUNT(*) FROM [dbo].[UserRoles]
    SELECT @InvalidRoles = COUNT(*) 
    FROM [dbo].[UserRoles] 
    WHERE [AccessLevel] IS NULL OR [AccessLevel] < 1 OR [AccessLevel] > 100
    
    IF @InvalidRoles = 0
    BEGIN
        PRINT '✓ All ' + CAST(@TotalRoles AS VARCHAR) + ' roles have valid AccessLevel values (1-100)'
    END
    ELSE
    BEGIN
        PRINT '❌ ' + CAST(@InvalidRoles AS VARCHAR) + ' roles have invalid AccessLevel values!'
        
        SELECT [ID], [Description], [AccessLevel]
        FROM [dbo].[UserRoles] 
        WHERE [AccessLevel] IS NULL OR [AccessLevel] < 1 OR [AccessLevel] > 100
    END
END

PRINT ''

-- Check 5: User role assignments
PRINT '5. USER ROLE ASSIGNMENTS:'
PRINT '-------------------------'
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
BEGIN
    SELECT 
        ur.[Description] AS RoleName,
        ISNULL(ur.[AccessLevel], 0) AS AccessLevel,
        COUNT(u.[LoginName]) AS UserCount
    FROM [dbo].[UserRoles] ur
    LEFT JOIN [dbo].[Users] u ON ur.[ID] = u.[RoleId]
    GROUP BY ur.[ID], ur.[Description], ur.[AccessLevel]
    ORDER BY ur.[AccessLevel] DESC
    
    -- Check for users with invalid role assignments
    DECLARE @OrphanedUsers INT
    SELECT @OrphanedUsers = COUNT(*)
    FROM [dbo].[Users] u
    LEFT JOIN [dbo].[UserRoles] ur ON u.[RoleId] = ur.[ID]
    WHERE u.[RoleId] IS NOT NULL AND ur.[ID] IS NULL
    
    IF @OrphanedUsers > 0
    BEGIN
        PRINT ''
        PRINT '⚠ WARNING: ' + CAST(@OrphanedUsers AS VARCHAR) + ' users have invalid role assignments!'
    END
END
ELSE
BEGIN
    PRINT 'Users table not found - skipping user assignment check'
END

PRINT ''

-- Summary
PRINT '6. MIGRATION STATUS SUMMARY:'
PRINT '----------------------------'
DECLARE @MigrationStatus VARCHAR(20) = 'UNKNOWN'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    IF NOT EXISTS (SELECT * FROM [dbo].[UserRoles] WHERE [AccessLevel] IS NULL OR [AccessLevel] < 1 OR [AccessLevel] > 100)
    BEGIN
        SET @MigrationStatus = 'COMPLETE'
    END
    ELSE
    BEGIN
        SET @MigrationStatus = 'PARTIAL'
    END
END
ELSE
BEGIN
    SET @MigrationStatus = 'NOT APPLIED'
END

PRINT 'Migration Status: ' + @MigrationStatus

CASE @MigrationStatus
    WHEN 'COMPLETE' THEN PRINT '✓ AccessLevel migration is fully applied and validated'
    WHEN 'PARTIAL' THEN PRINT '⚠ AccessLevel migration is partially applied - data issues detected'
    WHEN 'NOT APPLIED' THEN PRINT '❌ AccessLevel migration has not been applied yet'
END

PRINT ''
PRINT 'Verification completed at: ' + CONVERT(VARCHAR, GETDATE(), 120)

SET NOCOUNT OFF;
