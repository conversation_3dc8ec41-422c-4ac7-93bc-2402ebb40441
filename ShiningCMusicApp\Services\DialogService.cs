using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicApp.Components;
using ShiningCMusicApp.Components.Dialogs;

namespace ShiningCMusicApp.Services
{
    public class DialogService : IDialogService
    {
        private DeleteConfirmationDialog? _deleteDialog;
        private readonly List<DeleteConfirmationDialog> _dialogInstances = new();

        private AlertDialog? _alertDialog;
        private readonly List<AlertDialog> _alertDialogInstances = new();

        public void RegisterDialog(DeleteConfirmationDialog dialog)
        {
            _deleteDialog = dialog;
            _dialogInstances.Add(dialog);
        }

        public void UnregisterDialog(DeleteConfirmationDialog dialog)
        {
            _dialogInstances.Remove(dialog);
            if (_deleteDialog == dialog)
            {
                _deleteDialog = _dialogInstances.LastOrDefault();
            }
        }

        public void RegisterAlertDialog(AlertDialog dialog)
        {
            _alertDialog = dialog;
            _alertDialogInstances.Add(dialog);
        }

        public void UnregisterAlertDialog(AlertDialog dialog)
        {
            _alertDialogInstances.Remove(dialog);
            if (_alertDialog == dialog)
            {
                _alertDialog = _alertDialogInstances.LastOrDefault();
            }
        }

        public async Task<bool> ShowDeleteConfirmationAsync(
            string message,
            string? details = null,
            string title = "Confirm Delete",
            string confirmButtonText = "Delete",
            string cancelButtonText = "Cancel")
        {
            if (_deleteDialog == null)
            {
                throw new InvalidOperationException("No delete confirmation dialog is registered. Make sure to include DeleteConfirmationDialog component in your page.");
            }

            // Set the dialog properties
            _deleteDialog.Title = title;
            _deleteDialog.Message = message;
            _deleteDialog.Details = details;
            _deleteDialog.ConfirmButtonText = confirmButtonText;
            _deleteDialog.CancelButtonText = cancelButtonText;

            // Show the dialog and wait for result
            return await _deleteDialog.ShowAsync();
        }

        public async Task ShowAlertAsync(
            string message,
            string? details = null,
            string title = "Alert",
            AlertType type = AlertType.Info,
            string okButtonText = "OK")
        {
            if (_alertDialog == null)
            {
                throw new InvalidOperationException("No alert dialog is registered. Make sure to include AlertDialog component in your page.");
            }

            // Set the dialog properties
            _alertDialog.Title = title;
            _alertDialog.Message = message;
            _alertDialog.Details = details;
            _alertDialog.Type = type;
            _alertDialog.OkButtonText = okButtonText;

            // Show the dialog and wait for result
            await _alertDialog.ShowAsync();
        }

        public async Task ShowErrorAsync(string message, string? details = null, string title = "Error")
        {
            await ShowAlertAsync(message, details, title, AlertType.Error);
        }

        public async Task ShowSuccessAsync(string message, string? details = null, string title = "Success")
        {
            await ShowAlertAsync(message, details, title, AlertType.Success);
        }

        public async Task ShowWarningAsync(string message, string? details = null, string title = "Warning")
        {
            await ShowAlertAsync(message, details, title, AlertType.Warning);
        }
    }
}
