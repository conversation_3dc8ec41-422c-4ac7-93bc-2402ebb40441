using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicApp.Components;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Constants;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Extensions;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Utilities;

namespace ShiningCMusicApp.Pages;

public partial class AdminBase : CommonPageBase
{
    [Inject] protected IBffAdminService BffAdminService { get; set; } = default!;
    [Inject] protected IBffStudentsService BffStudents { get; set; } = default!;
    [Inject] protected IBffTutorsService BffTutors { get; set; } = default!;
    [Inject] private IBffSettingsService BffSettingsService { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;
    [Inject] protected AuthenticationStateProvider AuthenticationStateProvider { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;

    // Data properties
    protected List<User> users = new();
    protected List<UserRole> userRoles = new();
    protected List<Subject> subjects = new();
    protected List<Location> locations = new();
    protected List<Tutor> tutors = new();
    protected List<Student> students = new();
    protected bool isLoading = true;

    // User modal variables
    protected bool showUserModal = false;
    protected bool isEditUserMode = false;
    protected string userModalTitle = string.Empty;
    protected User currentUser = new();
    protected int? selectedTutorId = null;
    protected int? selectedStudentId = null;
    protected bool showAssignmentSection = false;
    protected List<string> passwordValidationErrors = new();
    protected string confirmPassword = string.Empty;
    protected string passwordMatchError = string.Empty;

    // UserRole modal variables
    protected bool showUserRoleModal = false;
    protected bool isEditUserRoleMode = false;
    protected string userRoleModalTitle = string.Empty;
    protected UserRole currentUserRole = new();

    // Subject modal variables
    protected bool showSubjectModal = false;
    protected bool isEditSubjectMode = false;
    protected bool isSaving = false;
    protected string subjectModalTitle = string.Empty;
    protected Subject currentSubject = new();

    // Location modal variables
    protected bool showLocationModal = false;
    protected bool isEditLocationMode = false;
    protected string locationModalTitle = string.Empty;
    protected Location currentLocation = new();

    // Other variables
    protected string activeBreakpoint = "Large";
    protected string columnWidth = "300";
    protected string gridKey = Guid.NewGuid().ToString();
    protected int selectedTabIndex = 0;
    protected IconStyle iconStyle;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await LoadConfig();
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load all admin data via BFF (single aggregated call)
            var adminData = await BffAdminService.GetAdminDataAsync();

            if (adminData != null)
            {
                users = adminData.Users;
                userRoles = adminData.UserRoles;
                subjects = adminData.Subjects;
                locations = adminData.Locations;
                tutors = adminData.Tutors;
                students = adminData.Students;

                var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                var authAccessLevel = authState.User.GetAccessLevel();

                users = users.Where(a => a.RoleAccessLevel <= authAccessLevel).ToList();
                userRoles = userRoles.Where(a => a.AccessLevel <= authAccessLevel).ToList();

                await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {users.Count} users, {userRoles.Count} userRoles, {subjects.Count} subjects, {locations.Count} locations, {tutors.Count} tutors, {students.Count} students via BFF");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("console.error", "Failed to load admin data from BFF");
                await DialogService.ShowErrorAsync("Error", "Failed to load admin data");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task LoadConfig()
    {
        try
        {
            // Load IconStyle from configuration
            var iconStyleString = await BffSettingsService.GetConfigValueAsync<string?>((int)ConfigGroupId.UI, "IconStyle", nameof(IconStyle.Circle));

            iconStyle = ConfigHelper.ParseIconStyle(iconStyleString);

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded IconStyle: {iconStyleString}");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.warn", $"Failed to load IconStyle config, using default values: {ex.Message}");
            iconStyle = IconStyle.Circle; // Fallback to default
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    // Subject CRUD Methods
    protected void OpenCreateSubjectModal()
    {
        currentSubject = new Subject();
        isEditSubjectMode = false;
        subjectModalTitle = "Create New Subject";
        showSubjectModal = true;
    }

    protected void OpenEditSubjectModal(Subject? subject)
    {
        if (subject != null)
        {
            currentSubject = new Subject
            {
                SubjectId = subject.SubjectId,
                SubjectName = subject.SubjectName
            };

            isEditSubjectMode = true;
            subjectModalTitle = "Edit Subject";
            showSubjectModal = true;
        }
    }

    protected void CloseSubjectModal()
    {
        showSubjectModal = false;
        currentSubject = new();
        isSaving = false;
    }

    protected async Task SaveSubject()
    {
        if (string.IsNullOrWhiteSpace(currentSubject.SubjectName))
        {
            await DialogService.ShowWarningAsync("Subject name is required.", "Please enter a valid subject name before saving.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditSubjectMode)
            {
                success = await BffAdminService.UpdateSubjectAsync(currentSubject);
            }
            else
            {
                success = await BffAdminService.CreateSubjectAsync(currentSubject);
            }

            if (success)
            {
                CloseSubjectModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save subject", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving subject: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving subject", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteSubject(Subject? subject)
    {
        if (subject == null) return;

        var message = $"Are you sure you want to delete subject '{subject.SubjectName}'?";
        var details = "This action cannot be undone.";

        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
            message,
            details,
            "Delete Subject");

        if (confirmed)
        {
            try
            {
                var success = await BffAdminService.DeleteSubjectAsync(subject.SubjectId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete subject", "Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting subject: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting subject", ex.Message);
            }
        }
    }

    // Location CRUD Methods
    protected void OpenCreateLocationModal()
    {
        currentLocation = new Location();
        isEditLocationMode = false;
        locationModalTitle = "Create New Location";
        showLocationModal = true;
    }

    protected void OpenEditLocationModal(Location? location)
    {
        if (location != null)
        {
            currentLocation = new Location
            {
                LocationId = location.LocationId,
                LocationName = location.LocationName
            };

            isEditLocationMode = true;
            locationModalTitle = "Edit Location";
            showLocationModal = true;
        }
    }

    protected void CloseLocationModal()
    {
        showLocationModal = false;
        currentLocation = new();
        isSaving = false;
    }

    protected async Task SaveLocation()
    {
        if (string.IsNullOrWhiteSpace(currentLocation.LocationName))
        {
            await DialogService.ShowWarningAsync("Location name is required.", "Please enter a valid location name before saving.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditLocationMode)
            {
                success = await BffAdminService.UpdateLocationAsync(currentLocation);
            }
            else
            {
                success = await BffAdminService.CreateLocationAsync(currentLocation);
            }

            if (success)
            {
                CloseLocationModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save location", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving location: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving location", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteLocation(Location? location)
    {
        if (location == null) return;

        var message = $"Are you sure you want to delete location '{location.LocationName}'?";
        var details = "This action cannot be undone.";

        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
            message,
            details,
            "Delete Location");

        if (confirmed)
        {
            try
            {
                var success = await BffAdminService.DeleteLocationAsync(location.LocationId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete location", "Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting location: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting location", ex.Message);
            }
        }
    }

    protected async Task OnBreakpointChanged()
    {
        try
        {
            if (activeBreakpoint == "Small")
            {
                columnWidth = "150";
            }
            else if (activeBreakpoint == "Medium" || activeBreakpoint == "Large")
            {
                columnWidth = "300";
            }
            gridKey = Guid.NewGuid().ToString();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnBreakPointChanged: {ex.Message}");
        }
    }

    protected string GetAccessLevelBadgeClass(int accessLevel)
    {
        if (accessLevel < 20) return IconStyleConstants.IconClass.ICON_BADGE_CLASS_ZERO;

        else if (accessLevel < 40) return IconStyleConstants.IconClass.ICON_BADGE_CLASS_LOW;

        return IconStyleConstants.IconClass.ICON_BADGE_CLASS_NORMAL;
    }
}
