namespace ShiningCMusicDbManager.Models
{
    /// <summary>
    /// Configuration options for database migrations
    /// </summary>
    public class DatabaseMigrationOptions
    {
        public string SqlScriptsPath { get; set; } = "SQL";
        public bool BackupBeforeMigration { get; set; } = false;
        public bool StopOnFirstFailure { get; set; } = false;
        public int CommandTimeoutSeconds { get; set; } = 300;
        public bool UseTransactions { get; set; } = true;
        public string BackupDirectory { get; set; } = "Backups";
        public bool VerboseLogging { get; set; } = true;
        public bool CreateBackupOnStart { get; set; } = false;
        public bool ValidateDependencies { get; set; } = true;
        public bool ShowExecutionTime { get; set; } = true;
        public bool ConfirmBeforeApply { get; set; } = true;
        public bool AutoApplyMigrations { get; set; } = false;
    }
}
