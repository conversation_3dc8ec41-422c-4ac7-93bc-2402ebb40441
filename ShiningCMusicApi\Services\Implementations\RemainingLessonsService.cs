using Dapper;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Data.SqlClient;
using System.Text.RegularExpressions;

namespace ShiningCMusicApi.Services.Implementations
{
    public class RemainingLessonsService : IRemainingLessonsService
    {
        private readonly string _connectionString;
        private readonly ILogger<RemainingLessonsService> _logger;

        public RemainingLessonsService(IConfiguration configuration, ILogger<RemainingLessonsService> logger)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
            _logger = logger;
        }

        public async Task<bool> UpdateStudentRemainingLessonsAsync(int studentId)
        {
            try
            {
                var remainingLessons = await GetRemainingLessonsForStudentByTimesheetsAsync(studentId);
                
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    UPDATE Students
                    SET RemainingLessons = @RemainingLessons,
                        UpdatedUTC = GETUTCDATE()
                    WHERE StudentId = @StudentId AND IsArchived = 0";

                var rowsAffected = await connection.ExecuteAsync(sql, new 
                { 
                    StudentId = studentId, 
                    RemainingLessons = remainingLessons 
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating remaining lessons for student {StudentId}", studentId);
                return false;
            }
        }

        public async Task<int> UpdateAllStudentsRemainingLessonsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT StudentId FROM Students WHERE IsArchived = 0";
                var studentIds = await connection.QueryAsync<int>(sql);
                
                var updatedCount = 0;
                foreach (var studentId in studentIds)
                {
                    var success = await UpdateStudentRemainingLessonsAsync(studentId);
                    if (success)
                    {
                        updatedCount++;
                    }
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating remaining lessons for all students");
                throw;
            }
        }

        public async Task<bool> UpdateTutorRemainingLessonsAsync(int tutorId)
        {
            try
            {
                var remainingLessons = await GetRemainingLessonsForTutorByTimesheetsAsync(tutorId);

                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    UPDATE Tutors
                    SET RemainingLessons = @RemainingLessons,
                        UpdatedUTC = GETUTCDATE()
                    WHERE TutorId = @TutorId AND IsArchived = 0";

                var rowsAffected = await connection.ExecuteAsync(sql, new
                {
                    TutorId = tutorId,
                    RemainingLessons = remainingLessons
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating remaining lessons for tutor {TutorId}", tutorId);
                return false;
            }
        }

        public async Task<int> UpdateAllTutorsRemainingLessonsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT TutorId FROM Tutors WHERE IsArchived = 0";
                var tutorIds = await connection.QueryAsync<int>(sql);

                var updatedCount = 0;
                foreach (var tutorId in tutorIds)
                {
                    var success = await UpdateTutorRemainingLessonsAsync(tutorId);
                    if (success)
                    {
                        updatedCount++;
                    }
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating remaining lessons for all tutors");
                throw;
            }
        }

        public async Task<int> GetRemainingLessonsForStudentByTimesheetsAsync(int studentId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT COUNT(*) AS RemainingLessons
                FROM Timesheets ts
                INNER JOIN TimesheetEntries tse ON tse.TimesheetId = ts.TimesheetId
                WHERE ts.IsArchived = 0
                AND tse.IsPresent = 0
                AND ts.StudentId = @StudentId
                GROUP BY ts.StudentId";

            return await connection.QueryFirstOrDefaultAsync<int>(sql, new { StudentId = studentId });
        }

        public async Task<int> GetRemainingLessonsForTutorByTimesheetsAsync(int tutorId)
        {
            using var connection = new SqlConnection(_connectionString);

            var currentDate = DateTime.UtcNow;

            var sql = @"
                SELECT COUNT(*) AS RemainingLessons
                FROM Timesheets ts
                INNER JOIN TimesheetEntries tse ON tse.TimesheetId = ts.TimesheetId
                WHERE ts.IsArchived = 0
                AND tse.IsPresent = 0
                AND ts.TutorId = @TutorId
                GROUP BY ts.TutorId";

            return await connection.QueryFirstOrDefaultAsync<int>(sql, new { TutorId = tutorId });
        }

        public async Task<int> GetRemainingLessonsForStudentAsync(int studentId)
        {
            using var connection = new SqlConnection(_connectionString);

            var currentDate = DateTime.UtcNow;

            // Get all future lessons for the student (both recurring and non-recurring), excluding cancelled ones
            var sql = @"
                SELECT LessonId, SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId,
                       IsRecurring, RecurrenceRule, RecurrenceID, RecurrenceException, CreatedUTC, UpdatedUTC, IsArchived, IsCancelled
                FROM Lessons
                WHERE StudentId = @StudentId
                AND IsArchived = 0
                AND IsCancelled = 0
                AND (
                    (IsRecurring = 0 AND StartTime > @CurrentDate) OR
                    (IsRecurring = 1 AND RecurrenceRule IS NOT NULL AND RecurrenceRule != '')
                )";

            var lessons = await connection.QueryAsync<Lesson>(sql, new { StudentId = studentId, CurrentDate = currentDate });

            int totalRemainingLessons = 0;

            foreach (var lesson in lessons)
            {
                if (!lesson.IsRecurring)
                {
                    // Simple future lesson
                    totalRemainingLessons++;
                }
                else
                {
                    if (lesson.RecurrenceID != null && !lesson.IsCancelled)
                    {
                        totalRemainingLessons++; // Count updated occurrence if it's not cancelled
                    }
                    else
                    {
                        // Calculate remaining occurrences for recurring lessons
                        totalRemainingLessons += CalculateRemainingOccurrences(lesson, currentDate);
                    }
                }
            }

            return totalRemainingLessons;
        }

        public async Task<int> GetRemainingLessonsForTutorAsync(int tutorId)
        {
            using var connection = new SqlConnection(_connectionString);

            var currentDate = DateTime.UtcNow;

            // Get all future lessons for the student (both recurring and non-recurring), excluding cancelled ones
            var sql = @"
                SELECT LessonId, SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId,
                       IsRecurring, RecurrenceRule, RecurrenceID, RecurrenceException, CreatedUTC, UpdatedUTC, IsArchived, IsCancelled
                FROM Lessons
                WHERE TutorId = @TutorId
                AND IsArchived = 0
                AND IsCancelled = 0
                AND (
                    (IsRecurring = 0 AND StartTime > @CurrentDate) OR
                    (IsRecurring = 1 AND RecurrenceRule IS NOT NULL AND RecurrenceRule != '')
                )";

            var lessons = await connection.QueryAsync<Lesson>(sql, new { TutorId = tutorId, CurrentDate = currentDate });

            int totalRemainingLessons = 0;

            foreach (var lesson in lessons)
            {
                if (!lesson.IsRecurring)
                {
                    // Simple future lesson
                    totalRemainingLessons++;
                }
                else
                {
                    if (lesson.RecurrenceID != null && !lesson.IsCancelled)
                    {
                        totalRemainingLessons++; // Count updated occurrence if it's not cancelled
                    }
                    else
                    {
                        // Calculate remaining occurrences for recurring lessons
                        totalRemainingLessons += CalculateRemainingOccurrences(lesson, currentDate);
                    }
                }
            }

            return totalRemainingLessons;
        }

        public async Task<IEnumerable<(int StudentId, string StudentName, string Email, int RemainingLessons)>> GetStudentsWithRemainingLessonsAsync(int lessonThreshold)
        {
            using var connection = new SqlConnection(_connectionString);

            // Get all active students (not archived and not excluded from emails)
            var studentsSql = @"
                SELECT StudentId, StudentName, Email, ExcludeEmail
                FROM Students
                WHERE IsArchived = 0 AND ExcludeEmail = 0 AND Email IS NOT NULL AND Email != ''";

            var students = await connection.QueryAsync<dynamic>(studentsSql);
            var result = new List<(int StudentId, string StudentName, string Email, int RemainingLessons)>();

            foreach (var student in students)
            {
                var remainingLessons = await GetRemainingLessonsForStudentByTimesheetsAsync(student.StudentId);

                if (remainingLessons <= lessonThreshold && remainingLessons > 0)
                {
                    result.Add((student.StudentId, student.StudentName ?? "", student.Email ?? "", remainingLessons));
                }
            }

            return result;
        }

        private int CalculateRemainingOccurrences(Lesson lesson, DateTime currentDate)
        {
            if (string.IsNullOrEmpty(lesson.RecurrenceRule))
                return 0;

            // If the entire lesson series is cancelled, return 0
            if (lesson.IsCancelled)
                return 0;

            try
            {
                var rrule = lesson.RecurrenceRule;
                var startTime = lesson.StartTime;
                var futureDate = currentDate.AddMonths(1);

                // Check if it has an UNTIL date
                var untilMatch = Regex.Match(rrule, @"UNTIL=(\d{8}T\d{6}Z?)");
                if (untilMatch.Success)
                {
                    var untilString = untilMatch.Groups[1].Value;
                    if (DateTime.TryParseExact(untilString.Replace("Z", ""), "yyyyMMddTHHmmss", null, System.Globalization.DateTimeStyles.None, out var untilDate))
                    {
                        futureDate = untilDate;
                        // Calculate remaining occurrences from current date to UNTIL date, excluding cancelled ones
                        return CalculateOccurrencesBetweenDatesWithExceptions(lesson, currentDate, futureDate);
                    }
                }

                // Check if it has a COUNT
                var countMatch = Regex.Match(rrule, @"COUNT=(\d+)");
                if (countMatch.Success && int.TryParse(countMatch.Groups[1].Value, out var totalCount))
                {
                    // Calculate how many occurrences have already passed (including cancelled ones)
                    var passedOccurrences = CalculateOccurrencesBetweenDates(lesson, startTime, currentDate);
                    var remainingFromCount = Math.Max(0, totalCount - passedOccurrences);

                    // Now subtract any cancelled future occurrences from the remaining count
                    futureDate = currentDate.AddMonths(12); // Look ahead 12 months for cancelled occurrences
                    var futureCancelledCount = CountCancelledOccurrencesBetweenDates(lesson, currentDate, futureDate);

                    return Math.Max(0, remainingFromCount - futureCancelledCount);
                }

                // If no COUNT or UNTIL, assume it's ongoing (return a reasonable number for payment reminders)
                // We'll calculate occurrences for the next 6 months as a reasonable estimate, excluding cancelled ones
                futureDate = currentDate.AddMonths(6);
                return CalculateOccurrencesBetweenDatesWithExceptions(lesson, currentDate, futureDate);
            }
            catch
            {
                return 0; // If we can't parse, assume no remaining lessons
            }
        }

        private int CalculateOccurrencesBetweenDates(Lesson lesson, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var rrule = lesson.RecurrenceRule;
                if (string.IsNullOrEmpty(rrule))
                    return 0;

                var startTime = lesson.StartTime;

                // Parse frequency and interval
                var freqMatch = Regex.Match(rrule, @"FREQ=(\w+)");
                var intervalMatch = Regex.Match(rrule, @"INTERVAL=(\d+)");

                if (!freqMatch.Success)
                    return 0;

                var frequency = freqMatch.Groups[1].Value.ToUpper();
                var interval = intervalMatch.Success ? int.Parse(intervalMatch.Groups[1].Value) : 1;

                int occurrences = 0;
                var currentOccurrence = startTime;

                // Start counting from the first occurrence after fromDate
                while (currentOccurrence < fromDate)
                {
                    switch (frequency)
                    {
                        case "DAILY":
                            currentOccurrence = currentOccurrence.AddDays(interval);
                            break;
                        case "WEEKLY":
                            currentOccurrence = currentOccurrence.AddDays(interval * 7);
                            break;
                        case "MONTHLY":
                            currentOccurrence = currentOccurrence.AddMonths(interval);
                            break;
                        case "YEARLY":
                            currentOccurrence = currentOccurrence.AddYears(interval);
                            break;
                        default:
                            return 0;
                    }
                }

                // Count occurrences between fromDate and toDate
                while (currentOccurrence <= toDate)
                {
                    if (currentOccurrence >= fromDate)
                    {
                        occurrences++;
                    }

                    switch (frequency)
                    {
                        case "DAILY":
                            currentOccurrence = currentOccurrence.AddDays(interval);
                            break;
                        case "WEEKLY":
                            currentOccurrence = currentOccurrence.AddDays(interval * 7);
                            break;
                        case "MONTHLY":
                            currentOccurrence = currentOccurrence.AddMonths(interval);
                            break;
                        case "YEARLY":
                            currentOccurrence = currentOccurrence.AddYears(interval);
                            break;
                        default:
                            return occurrences;
                    }
                }

                return occurrences;
            }
            catch
            {
                return 0;
            }
        }

        private int CalculateOccurrencesBetweenDatesWithExceptions(Lesson lesson, DateTime fromDate, DateTime toDate)
        {
            var totalOccurrences = CalculateOccurrencesBetweenDates(lesson, fromDate, toDate);
            var cancelledOccurrences = CountCancelledOccurrencesBetweenDates(lesson, fromDate, toDate);
            return Math.Max(0, totalOccurrences - cancelledOccurrences);
        }

        private int CountCancelledOccurrencesBetweenDates(Lesson lesson, DateTime fromDate, DateTime toDate)
        {
            if (string.IsNullOrEmpty(lesson.RecurrenceException))
                return 0;

            try
            {
                var cancelledCount = 0;
                var exceptionDates = ParseRecurrenceExceptionDates(lesson.RecurrenceException);

                foreach (var exceptionDate in exceptionDates)
                {
                    if (exceptionDate >= fromDate && exceptionDate <= toDate)
                    {
                        cancelledCount++;
                    }
                }

                return cancelledCount;
            }
            catch
            {
                return 0; // If we can't parse exceptions, assume no cancellations
            }
        }

        private List<DateTime> ParseRecurrenceExceptionDates(string recurrenceException)
        {
            var exceptionDates = new List<DateTime>();

            if (string.IsNullOrEmpty(recurrenceException))
                return exceptionDates;

            try
            {
                // RecurrenceException format is typically comma-separated dates in YYYYMMDDTHHMMSSZ format
                // Example: "20240115T100000Z,20240122T100000Z"
                var dateStrings = recurrenceException.Split(',', StringSplitOptions.RemoveEmptyEntries);

                foreach (var dateString in dateStrings)
                {
                    var cleanDateString = dateString.Trim().Replace("Z", "");

                    // Try different date formats that might be used
                    if (DateTime.TryParseExact(cleanDateString, "yyyyMMddTHHmmss", null, System.Globalization.DateTimeStyles.None, out var parsedDate))
                    {
                        exceptionDates.Add(parsedDate);
                    }
                    else if (DateTime.TryParseExact(cleanDateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var parsedDateOnly))
                    {
                        exceptionDates.Add(parsedDateOnly);
                    }
                }
            }
            catch
            {
                // If parsing fails, return empty list
            }

            return exceptionDates;
        }
    }
}
