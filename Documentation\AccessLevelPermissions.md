# Access Level Permission System

## Overview

The ShiningC Music App uses a numerical access level system for managing permissions. This approach provides more granular control over user permissions compared to traditional role-based access control (RBAC).

## Access Levels

The system defines the following base access levels:

```csharp
Level10 = 10;  // Student level access
Level20 = 20;  // Tutor level access
Level80 = 80;  // Manager level access
Level100 = 100; // Administrator level access
```

## Permission Structure

Permissions are organized by feature area in the `Permissions.cs` class. Each feature area defines specific access level requirements for different operations.

### Feature Areas

1. **Lessons**
   - View: Level 10+ (Students and above)
   - Edit: Level 80+ (Managers and above)
   - Create: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

2. **Timesheets**
   - View: Level 20+ (Tutors and above)
   - Create: Level 20+ (Tutors and above)
   - Edit: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

3. **Users**
   - View: Level 80+ (Managers and above)
   - Create: Level 80+ (Managers and above)
   - Edit: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

4. **Tutors**
   - View: Level 80+ (Managers and above)
   - Create: Level 80+ (Managers and above)
   - Edit: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

5. **Students**
   - View: Level 80+ (Managers and above)
   - Create: Level 80+ (Managers and above)
   - Edit: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

6. **Email Templates**
   - View: Level 80+ (Managers and above)
   - Create: Level 80+ (Managers and above)
   - Edit: Level 80+ (Managers and above)
   - Delete: Level 100 (Administrators only)

7. **System Settings**
   - View Settings: Level 80+ (Managers and above)
   - Edit Settings: Level 100 (Administrators only)
   - View Maintenance: Level 80+ (Managers and above)
   - Edit Maintenance: Level 100 (Administrators only)

## Implementation

### Authentication

The access level is stored in the user's claims during authentication:

```csharp
new Claim("AccessLevel", user.RoleAccessLevel?.ToString() ?? "0")
```

### Permission Checks

1. **In C# Code**

Using extension methods:
```csharp
// Check if user can view lessons
if (user.CanViewLessons())
{
    // Show lessons
}

// Check if user can edit timesheets
if (user.CanEditTimesheets())
{
    // Allow timesheet editing
}
```

Using direct access level checks:
```csharp
if (user.HasAccessLevel(Permissions.Level80))
{
    // Allow manager-level operations
}
```

2. **In Razor Views**

Using AuthorizeView with access level checks:
```razor
<AuthorizeView>
    <Authorized Context="authContext">
        @if (authContext.User.CanEditLessons())
        {
            <button @onclick="EditLesson">Edit</button>
        }
    </Authorized>
</AuthorizeView>
```

### Helper Methods

The `Permissions.Check` class provides helper methods for common permission checks:

```csharp
public static class Check
{
    public static bool CanViewLessons(int accessLevel) => accessLevel >= Lessons.View;
    public static bool CanEditLessons(int accessLevel) => accessLevel >= Lessons.Edit;
    // ... other permission checks
}
```

## Benefits

1. **Granular Control**: Easier to create intermediate access levels without changing code
2. **Hierarchical**: Higher levels automatically include permissions of lower levels
3. **Maintainable**: Central location for permission definitions
4. **Flexible**: Easy to modify permission requirements by changing access levels
5. **Clear**: Permission checks are explicit and self-documenting

## Best Practices

1. Always use the permission constants defined in `Permissions.cs` rather than hard-coding numbers
2. Use the extension methods for readability and consistency
3. Group related permissions in feature-specific sections
4. Document any new permission levels or changes
5. Consider the hierarchical nature when assigning permissions (higher levels include lower level access)

## Examples

### Adding New Permissions

To add new permission checks:

1. Define the permission level in the appropriate section of `Permissions.cs`:
```csharp
public static class Reports
{
    public const int View = Level20;    // Tutors can view reports
    public const int Create = Level80;  // Managers can create reports
    public const int Delete = Level100; // Admins can delete reports
}
```

2. Add helper methods in the `Check` class:
```csharp
public static bool CanViewReports(int accessLevel) => accessLevel >= Reports.View;
public static bool CanCreateReports(int accessLevel) => accessLevel >= Reports.Create;
public static bool CanDeleteReports(int accessLevel) => accessLevel >= Reports.Delete;
```

3. Add extension methods in `ClaimsPrincipalExtensions.cs`:
```csharp
public static bool CanViewReports(this ClaimsPrincipal user) => 
    Permissions.Check.CanViewReports(user.GetAccessLevel());
```

### Permission Matrix

| Operation        | Student (10) | Tutor (20) | Manager (80) | Admin (100) |
|-----------------|-------------|------------|--------------|-------------|
| View Lessons    | ✅          | ✅         | ✅           | ✅          |
| Edit Lessons    | ❌          | ❌         | ✅           | ✅          |
| View Timesheets | ❌          | ✅         | ✅           | ✅          |
| Edit Timesheets | ❌          | ❌         | ✅           | ✅          |
| System Settings | ❌          | ❌         | View Only    | ✅          |
