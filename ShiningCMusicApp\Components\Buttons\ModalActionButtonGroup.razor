@using Syncfusion.Blazor.Buttons

@if (ShowContainer)
{
    <div class="@ContainerClass">
        @ButtonsContent
    </div>
}
else
{
    @ButtonsContent
}

@code {
    private RenderFragment ButtonsContent => __builder =>
    {
        @if (ShowFirstButton)
        {
            <SfButton CssClass="@GetFirstButtonClass()"
                      @onclick="OnFirstClick"
                      Disabled="@(IsFirstDisabled || IsLoading)"
                      type="@FirstButtonType">
                @if (IsLoading)
                {
                    <span class="spinner-border spinner-border-sm" role="status"></span>
                }
                else if (!string.IsNullOrEmpty(FirstIcon))
                {
                    <i class="@FirstIcon" style="color: @FirstIconColor;"></i>
                    @if (!string.IsNullOrEmpty(FirstText))
                    {
                        <span class="ms-2">@FirstText</span>
                    }
                }
                @if (!IsLoading && string.IsNullOrEmpty(FirstIcon) && !string.IsNullOrEmpty(FirstText))
                {
                    <span>@FirstText</span>
                }
            </SfButton>
        }

        @if (ShowSecondButton)
        {
            <SfButton CssClass="@GetSecondButtonClass()"
                      @onclick="OnSecondClick"
                      Disabled="@IsSecondDisabled"
                      type="@SecondButtonType">
                @if (!string.IsNullOrEmpty(SecondIcon))
                {
                    <i class="@SecondIcon" style="color: @SecondIconColor;"></i>
                    @if (!string.IsNullOrEmpty(SecondText))
                    {
                        <span class="ms-2">@SecondText</span>
                    }
                }
                @if (string.IsNullOrEmpty(SecondIcon) && !string.IsNullOrEmpty(SecondText))
                {
                    <span>@SecondText</span>
                }
            </SfButton>
        }

        @if (ShowThirdButton)
        {
            <SfButton CssClass="@GetThirdButtonClass()"
                      @onclick="OnThirdClick"
                      Disabled="@IsThirdDisabled"
                      type="@ThirdButtonType">
                @if (!string.IsNullOrEmpty(ThirdIcon))
                {
                    <i class="@ThirdIcon" style="color: @ThirdIconColor;"></i>
                    @if (!string.IsNullOrEmpty(ThirdText))
                    {
                        <span class="ms-2">@ThirdText</span>
                    }
                }
                @if (string.IsNullOrEmpty(ThirdIcon) && !string.IsNullOrEmpty(ThirdText))
                {
                    <span>@ThirdText</span>
                }
            </SfButton>
        }
    };
}
