namespace ShiningCMusicCommon.Enums
{
    public static class BackgroundProcessorKeys
    {
        // Configuration Keys
        public const string LessonCleanup = "LessonCleanup";
        public const string TutorCleanup = "TutorCleanup";
        public const string StudentCleanup = "StudentCleanup";
        public const string TimesheetCleanup = "TimesheetCleanup";
        public const string PaymentReminder = "PaymentReminder";
        public const string EmailProcessor = "EmailProcessor";
        public const string RemainingLessonsUpdate = "RemainingLessonsUpdate";

        public const string LessonCleanupEnabled = "LessonCleanupEnabled";
        public const string TutorCleanupEnabled = "TutorCleanupEnabled";
        public const string StudentCleanupEnabled = "StudentCleanupEnabled";
        public const string TimesheetCleanupEnabled = "TimesheetCleanupEnabled";
        public const string PaymentReminderEnabled = "PaymentReminderEnabled";
        public const string EmailProcessorEnabled = "EmailProcessorEnabled";
        public const string RemainingLessonsUpdateEnabled = "RemainingLessonsUpdateEnabled";

        // Display Names
        public const string LessonCleanupDisplay = "Lesson Cleanup";
        public const string TutorCleanupDisplay = "Tutor Cleanup";
        public const string StudentCleanupDisplay = "Student Cleanup";
        public const string TimesheetCleanupDisplay = "Timesheet Cleanup";
        public const string PaymentReminderDisplay = "Payment Reminders";
        public const string EmailProcessorDisplay = "Email Processor";
        public const string RemainingLessonsUpdateDisplay = "Remaining Lessons Update";

        // Display Order
        public const int LessonCleanupDisplayOrder = 20;
        public const int TutorCleanupDisplayOrder = 30;
        public const int StudentCleanupDisplayOrder = 40;
        public const int TimesheetCleanupDisplayOrder = 50;
        public const int PaymentReminderDisplayOrder = 90;
        public const int EmailProcessorDisplayOrder = 10;
        public const int RemainingLessonsUpdateDisplayOrder = 60;

        // Descriptions
        public const string LessonCleanupDescription = "Automatically removes old archived lessons";
        public const string TutorCleanupDescription = "Automatically removes old archived tutors";
        public const string StudentCleanupDescription = "Automatically removes old archived students";
        public const string TimesheetCleanupDescription = "Automatically removes old archived timesheets";
        public const string PaymentReminderDescription = "Sends payment reminder emails to students";
        public const string EmailProcessorDescription = "Processes queued emails and sends them via SMTP";
        public const string RemainingLessonsUpdateDescription = "Updates remaining lessons count for all students";
    }

    public enum BackgroundProcessor
    {
        LessonCleanup,
        TutorCleanup,
        StudentCleanup,
        TimesheetCleanup,
        PaymentReminder,
        EmailProcessor,
        RemainingLessonsUpdate
    }
}
