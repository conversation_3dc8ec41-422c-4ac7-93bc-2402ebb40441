using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffStudentsService
    {
        // CRUD Operations
        Task<List<Student>> GetStudentsAsync();
        Task<Student?> GetStudentAsync(int id);
        Task<bool> CreateStudentAsync(Student student);
        Task<bool> UpdateStudentAsync(Student student);
        Task<bool> DeleteStudentAsync(int id);

        // Archive/Restore Operations
        Task<bool> ArchiveStudentAsync(int id);
        Task<bool> RestoreStudentAsync(int id);

        // Subject-based filtering
        Task<List<Student>> GetStudentsBySubjectAsync(int subjectId);

        // Remaining lessons
        Task<int> GetRemainingLessonsAsync(int studentId);
    }
}
