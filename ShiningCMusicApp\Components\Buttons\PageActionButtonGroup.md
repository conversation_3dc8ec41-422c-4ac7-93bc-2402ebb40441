# PageActionButtonGroup Component

A reusable Blazor component for creating consistent page-level action button groups with responsive text and conditional visibility.

## Features

- **Responsive Text**: Different text for desktop and mobile views
- **Conditional Visibility**: Show/hide buttons based on permissions or state
- **Bootstrap Integration**: Uses standard Bootstrap button classes
- **Flexible Styling**: Customizable button styles and container layout
- **Icon Support**: Bootstrap icon integration with customizable colors

## Basic Usage

### Two-Button Layout with Responsive Text
```razor
<PageActionButtonGroup
    ShowFirstButton="@CanCreateTimesheets"
    FirstTextDesktop="Add New Timesheet"
    FirstTextMobile="Add Timesheet"
    FirstIcon="bi bi-clipboard-plus"
    OnFirstClick="OpenCreateModal"
    SecondText="Refresh"
    SecondIcon="bi bi-arrow-clockwise"
    OnSecondClick="RefreshData" />
```

### Single Button
```razor
<PageActionButtonGroup
    FirstText="Export Data"
    FirstIcon="bi bi-download"
    OnFirstClick="ExportData"
    ShowSecondButton="false" />
```

### Three-Button Layout
```razor
<PageActionButtonGroup
    FirstText="Create"
    FirstIcon="bi bi-plus-circle"
    OnFirstClick="CreateNew"
    SecondText="Import"
    SecondIcon="bi bi-upload"
    OnSecondClick="ImportData"
    ShowThirdButton="true"
    ThirdText="Export"
    ThirdIcon="bi bi-download"
    OnThirdClick="ExportData" />
```

### Four-Button Layout
```razor
<PageActionButtonGroup
    FirstText="Create"
    FirstIcon="bi bi-plus-circle"
    OnFirstClick="CreateNew"
    SecondText="Import"
    SecondIcon="bi bi-upload"
    OnSecondClick="ImportData"
    ShowThirdButton="true"
    ThirdText="Export"
    ThirdIcon="bi bi-download"
    OnThirdClick="ExportData"
    ShowFourthButton="true"
    FourthText="Settings"
    FourthIcon="bi bi-gear"
    OnFourthClick="OpenSettings" />
```

## Parameters

### First Button
- `ShowFirstButton` (bool): Show/hide first button (default: true)
- `FirstText` (string): Button text for all screen sizes (default: "")
- `FirstTextDesktop` (string): Button text for desktop only (default: "")
- `FirstTextMobile` (string): Button text for mobile only (default: "")
- `FirstIcon` (string): Bootstrap icon class (default: "")
- `FirstIconColor` (string): Icon color (default: "white")
- `FirstButtonClass` (string): CSS class (default: "btn-primary")
- `FirstButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsFirstDisabled` (bool): Disable button (default: false)
- `OnFirstClick` (EventCallback): Click handler

### Second Button
- `ShowSecondButton` (bool): Show/hide second button (default: true)
- `SecondText` (string): Button text for all screen sizes (default: "")
- `SecondTextDesktop` (string): Button text for desktop only (default: "")
- `SecondTextMobile` (string): Button text for mobile only (default: "")
- `SecondIcon` (string): Bootstrap icon class (default: "")
- `SecondIconColor` (string): Icon color (default: "white")
- `SecondButtonClass` (string): CSS class (default: "btn-secondary")
- `SecondButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsSecondDisabled` (bool): Disable button (default: false)
- `OnSecondClick` (EventCallback): Click handler

### Third Button
- `ShowThirdButton` (bool): Show/hide third button (default: false)
- `ThirdText` (string): Button text for all screen sizes (default: "")
- `ThirdTextDesktop` (string): Button text for desktop only (default: "")
- `ThirdTextMobile` (string): Button text for mobile only (default: "")
- `ThirdIcon` (string): Bootstrap icon class (default: "")
- `ThirdIconColor` (string): Icon color (default: "white")
- `ThirdButtonClass` (string): CSS class (default: "btn-success")
- `ThirdButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsThirdDisabled` (bool): Disable button (default: false)
- `OnThirdClick` (EventCallback): Click handler

### Fourth Button
- `ShowFourthButton` (bool): Show/hide fourth button (default: false)
- `FourthText` (string): Button text for all screen sizes (default: "")
- `FourthTextDesktop` (string): Button text for desktop only (default: "")
- `FourthTextMobile` (string): Button text for mobile only (default: "")
- `FourthIcon` (string): Bootstrap icon class (default: "")
- `FourthIconColor` (string): Icon color (default: "white")
- `FourthButtonClass` (string): CSS class (default: "btn-info")
- `FourthButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsFourthDisabled` (bool): Disable button (default: false)
- `OnFourthClick` (EventCallback): Click handler

### General
- `ContainerClass` (string): Container CSS classes (default: "d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end")

## Text Display Logic

The component uses the following priority for text display:
1. If `FirstTextDesktop` is provided, it shows on desktop (sm and up)
2. If `FirstTextMobile` is provided, it shows on mobile (below sm)
3. If only `FirstText` is provided, it shows on all screen sizes
4. Icons are always shown if provided

## Migration from Existing Code

### Before
```razor
<div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
    @if (CanCreateTimesheets)
    {
        <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateModal">
            <i class="bi bi-clipboard-plus" style="color: white;"></i>
            <span class="d-none d-sm-inline ms-2">Add New Timesheet</span>
            <span class="d-sm-none ms-2">Add Timesheet</span>
        </button>
    }
    <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
        <i class="bi bi-arrow-clockwise me-2" style="color: white;"></i>Refresh
    </button>
</div>
```

### After
```razor
<PageActionButtonGroup
    ShowFirstButton="@CanCreateTimesheets"
    FirstTextDesktop="Add New Timesheet"
    FirstTextMobile="Add Timesheet"
    FirstIcon="bi bi-clipboard-plus"
    OnFirstClick="OpenCreateModal"
    SecondText="Refresh"
    SecondIcon="bi bi-arrow-clockwise"
    OnSecondClick="RefreshData" />
```

## Common Patterns

### Permission-Based Visibility
```razor
<PageActionButtonGroup
    ShowFirstButton="@CanCreate"
    ShowSecondButton="@CanEdit"
    ShowThirdButton="@CanDelete"
    ShowFourthButton="@CanManageSettings"
    ... />
```

### Dynamic Text with Variables
For dynamic text that includes C# expressions, use properties in your code-behind file:

**Code-behind (.razor.cs):**
```csharp
protected string FirstTextDesktop => $"Send Email to Selected ({selectedStudents.Count})";
protected string FirstTextMobile => $"Email ({selectedStudents.Count})";
```

**Razor markup:**
```razor
<PageActionButtonGroup
    ShowFirstButton="@(selectedStudents.Any())"
    FirstTextDesktop="@FirstTextDesktop"
    FirstTextMobile="@FirstTextMobile"
    FirstIcon="bi bi-envelope"
    OnFirstClick="ShowBulkEmailModal" />
```

**❌ Don't do this (causes compilation errors):**
```razor
<!-- This will cause "complex content" errors -->
<PageActionButtonGroup
    FirstTextDesktop="Send Email to Selected (@selectedStudents.Count)"
    FirstTextMobile="Email (@selectedStudents.Count)" />
```

### Custom Button Styles
```razor
<PageActionButtonGroup
    FirstButtonClass="btn-success"
    FirstButtonStyle="min-width: 150px; font-weight: bold;"
    ... />
```

### Compact Mobile Layout
```razor
<PageActionButtonGroup
    ContainerClass="d-flex flex-row gap-1 justify-content-end"
    FirstButtonStyle="min-width: auto; padding: 0.5rem;"
    ... />
```

## Available CSS Classes

The component supports all standard Bootstrap button classes:
- `btn-primary` (default primary)
- `btn-secondary` (default secondary)
- `btn-success`
- `btn-danger`
- `btn-warning`
- `btn-info`
- `btn-light`
- `btn-dark`

## Notes

- The component uses standard HTML buttons (not Syncfusion) for better performance on page-level actions
- Responsive text automatically switches based on Bootstrap breakpoints (sm = 576px)
- Icons are optional and will be properly spaced when text is present
- The default container class provides responsive flex layout that stacks on mobile
