using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Constants;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class RemainingLessonsUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<RemainingLessonsUpdateService> _logger;
        private readonly IConfiguration _configuration;
        private TimeSpan _period;
        private bool _isEnabled;

        public RemainingLessonsUpdateService(IServiceProvider serviceProvider, ILogger<RemainingLessonsUpdateService> logger, IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("RemainingLessonsUpdateService started");

            // Load configuration
            if (!await LoadConfigurationAsync())
            {
                _logger.LogError("Failed to load configuration. Service will not run.");
                return;
            }

            if (!_isEnabled)
            {
                _logger.LogInformation("RemainingLessonsUpdateService is disabled");
                return;
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await UpdateAllRemainingLessonsAsync();
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("RemainingLessonsUpdateService was cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred in RemainingLessonsUpdateService");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait 5 minutes before retrying
                }
            }
        }

        private async Task<bool> LoadConfigurationAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var configService = scope.ServiceProvider.GetRequiredService<IConfigService>();

                // Load configuration values with fallback to environment variables and appsettings.json
                var intervalMinutes = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "RemainingLessonsUpdateIntervalMinutes",
                    "REMAINING_LESSONS_UPDATE_INTERVAL_MINUTES", "RemainingLessonsUpdate:IntervalMinutes", 60);
                _isEnabled = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "RemainingLessonsUpdateEnabled",
                    "REMAINING_LESSONS_UPDATE_ENABLED", "RemainingLessonsUpdate:Enabled", true);

                _period = TimeSpan.FromMinutes(intervalMinutes);

                _logger.LogInformation("RemainingLessonsUpdateService configuration loaded: Interval={Minutes}m, Enabled={Enabled}",
                    intervalMinutes, _isEnabled);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load configuration from database, using fallback values.");

                // Fallback to original configuration method
                var intervalHours = GetConfigurationValue("REMAINING_LESSONS_UPDATE_INTERVAL_MINUTES", "RemainingLessonsUpdate:IntervalMinutes", 60);
                _isEnabled = GetConfigurationValue("REMAINING_LESSONS_UPDATE_ENABLED", "RemainingLessonsUpdate:Enabled", true);
                _period = TimeSpan.FromHours(intervalHours);
                return true;
            }
        }

        private async Task<int> GetConfigValueWithFallbackAsync(IConfigService configService, int groupId, string key,
            string envVar, string configKey, int defaultValue)
        {
            // Try database first
            var dbValue = await configService.GetConfigValueAsync<int?>(groupId, key);
            if (dbValue.HasValue)
            {
                _logger.LogInformation("Using database config {GroupId}:{Key} = {Value}", groupId, key, dbValue.Value);
                return dbValue.Value;
            }

            // Fall back to environment variable
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", envVar, envIntValue);
                return envIntValue;
            }

            // Fall back to appsettings.json
            var configValue = _configuration.GetValue<int?>(configKey);
            if (configValue.HasValue)
            {
                _logger.LogInformation("Using appsettings.json config {ConfigKey} = {Value}", configKey, configValue.Value);
                return configValue.Value;
            }

            _logger.LogInformation("Using default value for {Key} = {Value}", key, defaultValue);
            return defaultValue;
        }

        private async Task<bool> GetConfigValueWithFallbackAsync(IConfigService configService, int groupId, string key,
            string envVar, string configKey, bool defaultValue)
        {
            // Try database first
            var dbValue = await configService.GetConfigValueAsync<bool?>(groupId, key);
            if (dbValue.HasValue)
            {
                _logger.LogInformation("Using database config {GroupId}:{Key} = {Value}", groupId, key, dbValue.Value);
                return dbValue.Value;
            }

            // Fall back to environment variable
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && bool.TryParse(envValue, out var envBoolValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", envVar, envBoolValue);
                return envBoolValue;
            }

            // Fall back to appsettings.json
            var configValue = _configuration.GetValue<bool?>(configKey);
            if (configValue.HasValue)
            {
                _logger.LogInformation("Using appsettings.json config {ConfigKey} = {Value}", configKey, configValue.Value);
                return configValue.Value;
            }

            _logger.LogInformation("Using default value for {Key} = {Value}", key, defaultValue);
            return defaultValue;
        }

        private int GetConfigurationValue(string envVar, string configKey, int defaultValue)
        {
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                return envIntValue;
            }

            return _configuration.GetValue(configKey, defaultValue);
        }

        private bool GetConfigurationValue(string envVar, string configKey, bool defaultValue)
        {
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && bool.TryParse(envValue, out var envBoolValue))
            {
                return envBoolValue;
            }

            return _configuration.GetValue(configKey, defaultValue);
        }

        public async Task UpdateAllRemainingLessonsAsync()
        {
            _logger.LogInformation("Starting remaining lessons update for all students and tutors...");

            using var scope = _serviceProvider.CreateScope();
            var remainingLessonsService = scope.ServiceProvider.GetRequiredService<IRemainingLessonsService>();

            try
            {
                var updatedStudents = await remainingLessonsService.UpdateAllStudentsRemainingLessonsAsync();
                _logger.LogInformation("Updated {UpdatedCount} students", updatedStudents);

                var updatedTutors = await remainingLessonsService.UpdateAllTutorsRemainingLessonsAsync();
                _logger.LogInformation("Updated {UpdatedCount} tutors", updatedTutors);

                _logger.LogInformation("Remaining lessons update completed for students and tutors.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating remaining lessons for students and tutors");
                throw;
            }
        }
    }
}
