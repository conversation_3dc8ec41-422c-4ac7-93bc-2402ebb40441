using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IEmailQueueService
    {
        /// <summary>
        /// Queue an email for delivery
        /// </summary>
        Task<int> QueueEmailAsync(EmailQueueRequest request);

        /// <summary>
        /// Queue an email for delivery with specific delivery time
        /// </summary>
        Task<int> QueueEmailAsync(string toEmail, string subject, string body, bool isHtml = true,
            string? cc = null, string? bcc = null, DateTime? deliverAfter = null,
            List<EmailQueueAttachmentRequest>? attachments = null);

        /// <summary>
        /// Get pending emails ready for delivery
        /// </summary>
        Task<IEnumerable<EmailQueue>> GetPendingEmailsAsync(int maxAttempts);

        /// <summary>
        /// Mark email as sent successfully
        /// </summary>
        Task<bool> MarkEmailAsSentAsync(int emailId);

        /// <summary>
        /// Increment send attempt count
        /// </summary>
        Task<bool> IncrementSendAttemptAsync(int emailId);

        /// <summary>
        /// Cancel a queued email
        /// </summary>
        Task<bool> CancelEmailAsync(int emailId);

        /// <summary>
        /// Clean up old sent emails
        /// </summary>
        Task<int> CleanupOldEmailsAsync(int olderThanDays);

        /// <summary>
        /// Get email queue statistics
        /// </summary>
        Task<EmailQueueStats> GetQueueStatsAsync();

        /// <summary>
        /// Add attachment to queued email
        /// </summary>
        Task<bool> AddAttachmentAsync(int emailQueueId, EmailQueueAttachmentRequest attachment);

        /// <summary>
        /// Get attachments for a queued email
        /// </summary>
        Task<IEnumerable<EmailQueueAttachment>> GetEmailAttachmentsAsync(int emailQueueId);
    }

    public class EmailQueueStats
    {
        public int PendingCount { get; set; }
        public int SentTodayCount { get; set; }
        public int FailedCount { get; set; }
        public int CancelledCount { get; set; }
    }
}
