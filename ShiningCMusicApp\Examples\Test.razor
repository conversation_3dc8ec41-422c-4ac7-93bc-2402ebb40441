﻿@page "/test"
@using Syncfusion.Blazor.Schedule
using ShiningCMusicCommon.Utilities;
@inject IJSRuntime JSRuntime

<h3>Test Recurring Events - Edit Single Occurrence vs Entire Series</h3>

<SfSchedule TValue="AppointmentData" Height="550px" @bind-SelectedDate="@CurrentDate">
    <ScheduleEvents TValue="AppointmentData" OnActionBegin="OnActionBegin" ActionCompleted="OnActionComplete"></ScheduleEvents>
    <ScheduleEventSettings 
        DataSource="@DataSource"
        AllowEditFollowingEvents="true">
    </ScheduleEventSettings>
    <ScheduleViews>
        <ScheduleView Option="View.Day"></ScheduleView>
        <ScheduleView Option="View.Week"></ScheduleView>
        <ScheduleView Option="View.WorkWeek"></ScheduleView>
        <ScheduleView Option="View.Month"></ScheduleView>
        <ScheduleView Option="View.Agenda"></ScheduleView>
    </ScheduleViews>
</SfSchedule>

<div class="mt-3">
    <h5>Instructions:</h5>
    <ul>
        <li>Double-click on any recurring event (Daily Meeting)</li>
        <li>You should see a dialog asking "Edit this occurrence" vs "Edit entire series"</li>
        <li>Check browser console for logs showing which option was selected</li>
    </ul>
</div>

@code {
    DateTime CurrentDate = new DateTime(2025, 1, 6);
    Dictionary<int, string> originalRules = new();

    List<AppointmentData> DataSource = new List<AppointmentData>
    {
        // Parent recurring event - Daily meeting for 5 days
        new AppointmentData {
            Id = 1,
            Subject = "Daily Meeting",
            StartTime = new DateTime(2025, 1, 6, 9, 30, 0),
            EndTime = new DateTime(2025, 1, 6, 11, 0, 0),
            RecurrenceRule = "FREQ=DAILY;INTERVAL=1;COUNT=5"
        },
        // Single non-recurring event for comparison
        new AppointmentData {
            Id = 3,
            Subject = "One-time Meeting",
            StartTime = new DateTime(2025, 1, 8, 14, 0, 0),
            EndTime = new DateTime(2025, 1, 8, 15, 0, 0)
        }
    };

    private async Task OnActionBegin(ActionEventArgs<AppointmentData> args)
    {
        await JSRuntime.InvokeVoidAsync("console.log", $"OnActionBegin - ActionType: {args.ActionType}");

        if (args.ActionType == ActionType.EventChange)
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"AddedRecords: {args.AddedRecords?.Count() ?? 0}");
            await JSRuntime.InvokeVoidAsync("console.log", $"ChangedRecords: {args.ChangedRecords?.Count() ?? 0}");
        }
    }

    private async Task OnActionComplete(ActionEventArgs<AppointmentData> args)
    {
        await JSRuntime.InvokeVoidAsync("console.log", $"OnActionComplete - ActionType: {args.ActionType}");

        if (args.ActionType == ActionType.EventChange)
        {
            // Check for single occurrence edit (AddedRecords with RecurrenceID)
            if (args.AddedRecords?.Any(r => r.RecurrenceID.HasValue) == true)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "✅ USER SELECTED: Edit Single Occurrence");
                foreach (var added in args.AddedRecords)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"New exception event - ID: {added.Id}, RecurrenceID: {added.RecurrenceID}, Subject: {added.Subject}");
                }
            }

            // Check for series edit (ChangedRecords with RecurrenceRule)
            if (args.ChangedRecords?.Any(r => !string.IsNullOrEmpty(r.RecurrenceRule)) == true)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "✅ USER SELECTED: Edit Entire Series");
                foreach (var changed in args.ChangedRecords)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Updated series - ID: {changed.Id}, RecurrenceRule: {changed.RecurrenceRule}");
                }
            }

            // Check for exception updates (ChangedRecords with RecurrenceException)
            if (args.ChangedRecords?.Any(r => !string.IsNullOrEmpty(r.RecurrenceException)) == true)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "✅ Parent event updated with exception");
                foreach (var changed in args.ChangedRecords)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Exception added - ID: {changed.Id}, RecurrenceException: {changed.RecurrenceException}");
                }
            }
        }
    }

    public class AppointmentData
    {
        public int Id { get; set; }
        public string Subject { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool IsAllDay { get; set; }
        public string? RecurrenceRule { get; set; }
        public string? RecurrenceException { get; set; }
        public Nullable<int> RecurrenceID { get; set; }
    }
}