/* Lessons page specific styles */

/* Schedule responsive height */
.schedule-container .e-schedule {
    height: auto !important;
}

/* <PERSON>tor Colors collapse styling */
.card-header .btn-link {
    color: inherit !important;
    text-decoration: none !important;
}

.card-header .btn-link:hover {
    color: inherit !important;
}

.card-header .btn-link i.bi-chevron-down {
    transition: transform 0.2s ease;
}

.card-header .btn-link[aria-expanded="true"] i.bi-chevron-down {
    transform: rotate(180deg);
}

/* Custom footer styling */
.custom-editor-footer {
    display: flex;
    justify-content: flex-end;
}

/* Mobile lesson card styling */
.clickable-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #dee2e6;
}

    .clickable-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #0066cc;
    }

    .clickable-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

.lesson-card {
    border-radius: 8px;
}

    .lesson-card .card-body {
        position: relative;
    }

/* Ensure editor dialog appears above mobile list view */
.e-schedule-dialog {
    z-index: 1049 !important;
}

    .e-schedule-dialog .e-dlg-overlay {
        z-index: 1048 !important;
    }

/* Custom QuickInfo footer styling */
.custom-quick-info-footer {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 12px 16px;
    border-top: 1px solid #eee;
    /*background-color: #f8f9fa;*/
}

.custom-quick-info-footer .custom-edit-btn,
.custom-quick-info-footer .custom-delete-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.custom-quick-info-footer .custom-edit-btn {
    border-color: #0066cc;
    color: #0066cc;
}

.custom-quick-info-footer .custom-edit-btn:hover {
    background-color: #0066cc;
    border-color: #0066cc;
    color: white;
}

.custom-quick-info-footer .custom-delete-btn {
    border-color: #dc3545;
    color: #dc3545;
}

.custom-quick-info-footer .custom-delete-btn:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

button.custom-edit-btn:disabled,
button.custom-delete-btn:disabled {
    background-color: #e0e0e0;
    color: #6c757d;
    border-color: #d6d6d6;
    opacity: 1;
    pointer-events: none;
}

/* Style the button container */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer {
    display: flex !important;
    gap: 8px !important;
    justify-content: flex-end !important;
    padding: 16px !important;
    border-top: 1px solid #eee !important;
}

/* Import/Export button styling */
.btn-group .dropdown-toggle {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

.dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.dropdown-menu .dropdown-item i {
    width: 16px;
    text-align: center;
}

.dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Custom Import Button Styling */
.import-button-container {
    position: relative;
    display: inline-block;
}

.import-button-container input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 2;
}

.import-button-container .btn {
    position: relative;
    z-index: 1;
    pointer-events: none;
    /* Ensure same size as export button */
    min-width: auto;
    height: auto;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.375rem;
}

/* Import button hover effect - triggered by file input hover */
.import-button-container:hover .btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.15s ease-in-out;
}

/* Import button active effect - triggered by file input active */
.import-button-container:active .btn {
    background-color: #0a58ca;
    border-color: #0a53be;
    color: #fff;
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.125);
}

/* Make import and export buttons exactly the same size */
.btn-group .btn.dropdown-toggle,
.import-button-container .btn {
    min-width: 150px !important;
    height: 38px !important;
    padding: 0.375rem 0.25rem !important;
    font-size: 0.875rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Focus effects for accessibility */
.import-button-container input[type="file"]:focus + .btn {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: none;
}

.btn-group .btn.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Lesson action buttons styling */
.lesson-action-btn {
    min-width: 150px;
}

/* Custom recurrence editor styling */
.custom-recurrence-editor {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

/* Clickable tutor name styling */
.tutor-name-clickable {
    transition: all 0.2s ease-in-out;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    display: inline-block;
    margin: -0.25rem -0.5rem;
    margin-left: -1.25rem;
}

.tutor-name-clickable:hover {
    background-color: rgba(0, 102, 204, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tutor-name-clickable:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-primary {
    white-space: normal; /* Allows text to wrap to the next line */
    overflow-wrap: break-word; /* Breaks long words to wrap within the container */
    word-break: break-word; /* Ensures long words break and wrap */
    max-width: 100%; /* Ensures the element doesn't exceed its container */
}

/* Selected tutor name styling */
.tutor-name-clickable.fw-bold.text-primary {
    background-color: rgba(0, 102, 204, 0.15);
    border: 1px solid rgba(0, 102, 204, 0.3);
}

.tutor-name-clickable.fw-bold.text-primary:hover {
    background-color: rgba(0, 102, 204, 0.2);
}

/* Multi-selection visual enhancement */
.tutor-name-clickable:not(.fw-bold):hover {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

/* Add a subtle border to indicate clickable state */
.tutor-name-clickable {
    border: 1px solid transparent;
}

/* Unselected state - make it look more like a button */
.tutor-name-clickable:not(.fw-bold) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #dee2e6;
}

.tutor-name-clickable:not(.fw-bold):hover {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
    border-color: #28a745;
    color: #155724 !important;
}

/* Additional breakpoint for very small screens */
@media (max-width: 576px) {
    .d-flex.gap-2.flex-wrap,
    .d-flex.gap-2.flex-wrap > *,
    .d-flex.gap-2.flex-wrap .btn,
    .d-flex.gap-2.flex-wrap button,
    .lesson-action-btn {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        display: block !important;
    }
}

/* High specificity override for mobile - target the exact container */
@media screen and (max-width: 768px) {
    .card-header .d-flex.gap-2.flex-wrap,
    .card-body .d-flex.gap-2.flex-wrap {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
    }

    .custom-editor-footer {
        flex-direction: column !important;
        gap: 0.5rem !important; /* space between buttons */
    }
}
