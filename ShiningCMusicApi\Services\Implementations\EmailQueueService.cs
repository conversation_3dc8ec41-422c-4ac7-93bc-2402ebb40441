using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class EmailQueueService : IEmailQueueService
    {
        private readonly string _connectionString;
        private readonly ILogger<EmailQueueService> _logger;

        public EmailQueueService(IConfiguration configuration, ILogger<EmailQueueService> logger)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
            _logger = logger;
        }

        public async Task<int> QueueEmailAsync(EmailQueueRequest request)
        {
            return await QueueEmailAsync(
                request.ToEmail,
                request.Subject,
                request.BodyHtml ?? request.BodyText ?? "",
                !string.IsNullOrWhiteSpace(request.BodyHtml),
                request.CC,
                request.BCC,
                request.DeliverAfterUTC,
                request.Attachments
            );
        }

        public async Task<int> QueueEmailAsync(string toEmail, string subject, string body, bool isHtml = true,
            string? cc = null, string? bcc = null, DateTime? deliverAfter = null,
            List<EmailQueueAttachmentRequest>? attachments = null)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                var sql = @"
                    INSERT INTO EmailQueue (ToEmail, CC, BCC, Subject, BodyText, BodyHtml, CreatedUTC, DeliverAfterUTC, SentAttempts, Cancelled)
                    VALUES (@ToEmail, @CC, @BCC, @Subject, @BodyText, @BodyHtml, GETUTCDATE(), @DeliverAfterUTC, 0, 0);

                    SELECT SCOPE_IDENTITY();";

                var emailId = await connection.QuerySingleAsync<int>(sql, new
                {
                    ToEmail = toEmail,
                    CC = cc,
                    BCC = bcc,
                    Subject = subject,
                    BodyText = isHtml ? null : body,
                    BodyHtml = isHtml ? body : null,
                    DeliverAfterUTC = deliverAfter ?? DateTime.UtcNow
                }, transaction);

                // Add attachments if provided
                if (attachments != null && attachments.Any())
                {
                    var attachmentSql = @"
                        INSERT INTO EmailQueueAttachments (EmailQueueId, AttachmentName, AttachmentPath, ContentType, FileSizeBytes, CreatedUTC)
                        VALUES (@EmailQueueId, @AttachmentName, @AttachmentPath, @ContentType, @FileSizeBytes, GETUTCDATE())";

                    foreach (var attachment in attachments)
                    {
                        await connection.ExecuteAsync(attachmentSql, new
                        {
                            EmailQueueId = emailId,
                            attachment.AttachmentName,
                            attachment.AttachmentPath,
                            attachment.ContentType,
                            attachment.FileSizeBytes
                        }, transaction);
                    }
                }

                transaction.Commit();
                _logger.LogInformation("Email queued successfully with ID {EmailId} for {ToEmail} with {AttachmentCount} attachments",
                    emailId, toEmail, attachments?.Count ?? 0);
                return emailId;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<IEnumerable<EmailQueue>> GetPendingEmailsAsync(int maxAttempts)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT TOP 50 * FROM EmailQueue 
                WHERE Cancelled = 0 
                AND SentUTC IS NULL 
                AND SentAttempts < @MaxAttempts 
                AND DeliverAfterUTC <= GETUTCDATE()
                ORDER BY CreatedUTC ASC";

            return await connection.QueryAsync<EmailQueue>(sql, new { MaxAttempts = maxAttempts });
        }

        public async Task<bool> MarkEmailAsSentAsync(int emailId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE EmailQueue 
                SET SentUTC = GETUTCDATE() 
                WHERE ID = @EmailId";

            var rowsAffected = await connection.ExecuteAsync(sql, new { EmailId = emailId });
            return rowsAffected > 0;
        }

        public async Task<bool> IncrementSendAttemptAsync(int emailId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE EmailQueue 
                SET SentAttempts = SentAttempts + 1 
                WHERE ID = @EmailId";

            var rowsAffected = await connection.ExecuteAsync(sql, new { EmailId = emailId });
            return rowsAffected > 0;
        }

        public async Task<bool> CancelEmailAsync(int emailId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE EmailQueue 
                SET Cancelled = 1 
                WHERE ID = @EmailId AND SentUTC IS NULL";

            var rowsAffected = await connection.ExecuteAsync(sql, new { EmailId = emailId });
            return rowsAffected > 0;
        }

        public async Task<int> CleanupOldEmailsAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM EmailQueue 
                WHERE SentUTC IS NOT NULL 
                AND CreatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var rowsAffected = await connection.ExecuteAsync(sql, new { OlderThanDays = olderThanDays });
            
            if (rowsAffected > 0)
            {
                _logger.LogInformation("Cleaned up {Count} old email records older than {Days} days", rowsAffected, olderThanDays);
            }

            return rowsAffected;
        }

        public async Task<EmailQueueStats> GetQueueStatsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    COUNT(CASE WHEN SentUTC IS NULL AND Cancelled = 0 THEN 1 END) as PendingCount,
                    COUNT(CASE WHEN SentUTC IS NOT NULL AND CAST(SentUTC AS DATE) = CAST(GETUTCDATE() AS DATE) THEN 1 END) as SentTodayCount,
                    COUNT(CASE WHEN SentUTC IS NULL AND SentAttempts >= 5 AND Cancelled = 0 THEN 1 END) as FailedCount,
                    COUNT(CASE WHEN Cancelled = 1 THEN 1 END) as CancelledCount
                FROM EmailQueue";

            return await connection.QuerySingleAsync<EmailQueueStats>(sql);
        }

        public async Task<bool> AddAttachmentAsync(int emailQueueId, EmailQueueAttachmentRequest attachment)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO EmailQueueAttachments (EmailQueueId, AttachmentName, AttachmentPath, ContentType, FileSizeBytes, CreatedUTC)
                VALUES (@EmailQueueId, @AttachmentName, @AttachmentPath, @ContentType, @FileSizeBytes, GETUTCDATE())";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                EmailQueueId = emailQueueId,
                attachment.AttachmentName,
                attachment.AttachmentPath,
                attachment.ContentType,
                attachment.FileSizeBytes
            });

            return rowsAffected > 0;
        }

        public async Task<IEnumerable<EmailQueueAttachment>> GetEmailAttachmentsAsync(int emailQueueId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT ID, EmailQueueId, AttachmentName, AttachmentPath, ContentType, FileSizeBytes, CreatedUTC
                FROM EmailQueueAttachments
                WHERE EmailQueueId = @EmailQueueId
                ORDER BY CreatedUTC";

            return await connection.QueryAsync<EmailQueueAttachment>(sql, new { EmailQueueId = emailQueueId });
        }
    }
}
