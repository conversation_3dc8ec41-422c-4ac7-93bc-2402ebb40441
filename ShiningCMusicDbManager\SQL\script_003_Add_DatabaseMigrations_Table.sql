/*
==============================================================================
SHINING C MUSIC - DATABASE MIGRATION TRACKING TABLE
==============================================================================
Script Name: Add_DatabaseMigrations_Table.sql
Purpose: Create the DatabaseMigrations table for tracking applied migrations
Version: 1.0
Date: 2025-01-23
Author: Database Migration System

DESCRIPTION:
This script creates the DatabaseMigrations table that tracks which migration
scripts have been applied to the database, when they were applied, and their
execution status.

FEATURES:
- Tracks migration script names and execution status
- Records execution time and error messages
- Uses SHA256 hashing to detect script changes
- Supports rollback tracking
- Includes audit fields for troubleshooting

USAGE:
Run this script to set up the migration tracking system before using the
DatabaseMigrationService.
==============================================================================
*/

USE [MusicSchool]
GO

SET NOCOUNT ON;

PRINT 'Setting up Database Migration Tracking System...'
PRINT '================================================'
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- Check if table already exists
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND type in (N'U'))
BEGIN
    PRINT '1. Creating DatabaseMigrations table...'
    
    CREATE TABLE [dbo].[DatabaseMigrations] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [ScriptName] NVARCHAR(255) NOT NULL,
        [AppliedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Success',
        [ExecutionTimeMs] BIGINT NULL,
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [ScriptHash] NVARCHAR(64) NOT NULL,
        [AppliedBy] NVARCHAR(100) NULL DEFAULT 'System',
        [ScriptCategory] NVARCHAR(50) NULL,
        [BatchesExecuted] INT NULL DEFAULT 0,
        [IsRollback] BIT NOT NULL DEFAULT 0,
        [RolledBackDate] DATETIME2 NULL,
        [RolledBackBy] NVARCHAR(100) NULL,
        [Dependencies] NVARCHAR(500) NULL,
        [FileSize] BIGINT NULL,
        [LastModified] DATETIME2 NULL,
        [Notes] NVARCHAR(1000) NULL,
        
        CONSTRAINT [PK_DatabaseMigrations] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UQ_DatabaseMigrations_ScriptName] UNIQUE NONCLUSTERED ([ScriptName] ASC)
    );
    
    PRINT '   ✓ DatabaseMigrations table created successfully'
END
ELSE
BEGIN
    PRINT '1. DatabaseMigrations table already exists'
    
    -- Check if we need to add any missing columns
    DECLARE @ColumnCount INT = 0;
    
    -- Check for IsRollback column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'IsRollback')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [IsRollback] BIT NOT NULL DEFAULT 0;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added IsRollback column'
    END
    
    -- Check for RolledBackDate column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'RolledBackDate')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [RolledBackDate] DATETIME2 NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added RolledBackDate column'
    END
    
    -- Check for RolledBackBy column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'RolledBackBy')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [RolledBackBy] NVARCHAR(100) NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added RolledBackBy column'
    END
    
    -- Check for Dependencies column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'Dependencies')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [Dependencies] NVARCHAR(500) NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added Dependencies column'
    END
    
    -- Check for FileSize column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'FileSize')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [FileSize] BIGINT NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added FileSize column'
    END
    
    -- Check for LastModified column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'LastModified')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [LastModified] DATETIME2 NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added LastModified column'
    END
    
    -- Check for Notes column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = 'Notes')
    BEGIN
        ALTER TABLE [dbo].[DatabaseMigrations] ADD [Notes] NVARCHAR(1000) NULL;
        SET @ColumnCount = @ColumnCount + 1;
        PRINT '   ✓ Added Notes column'
    END
    
    IF @ColumnCount = 0
    BEGIN
        PRINT '   ✓ Table schema is up to date'
    END
    ELSE
    BEGIN
        PRINT CONCAT('   ✓ Updated table schema (added ', @ColumnCount, ' columns)')
    END
END

-- Create indexes for better performance
PRINT ''
PRINT '2. Creating indexes...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = N'IX_DatabaseMigrations_AppliedDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_AppliedDate] 
    ON [dbo].[DatabaseMigrations] ([AppliedDate] DESC);
    PRINT '   ✓ Created index on AppliedDate'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = N'IX_DatabaseMigrations_Status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_Status] 
    ON [dbo].[DatabaseMigrations] ([Status] ASC);
    PRINT '   ✓ Created index on Status'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = N'IX_DatabaseMigrations_ScriptHash')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_ScriptHash] 
    ON [dbo].[DatabaseMigrations] ([ScriptHash] ASC);
    PRINT '   ✓ Created index on ScriptHash'
END

-- Create a view for easy querying
PRINT ''
PRINT '3. Creating migration status view...'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_MigrationStatus]'))
BEGIN
    DROP VIEW [dbo].[vw_MigrationStatus];
END

EXEC('
CREATE VIEW [dbo].[vw_MigrationStatus] AS
SELECT 
    [ScriptName],
    [Status],
    [AppliedDate],
    [ExecutionTimeMs],
    [AppliedBy],
    [ScriptCategory],
    [BatchesExecuted],
    [IsRollback],
    [RolledBackDate],
    [RolledBackBy],
    [ErrorMessage],
    CASE 
        WHEN [Status] = ''Success'' AND [RolledBackDate] IS NULL THEN ''Applied''
        WHEN [Status] = ''Success'' AND [RolledBackDate] IS NOT NULL THEN ''Rolled Back''
        WHEN [Status] = ''Failed'' THEN ''Failed''
        ELSE ''Unknown''
    END AS [CurrentStatus],
    CASE 
        WHEN [ExecutionTimeMs] < 1000 THEN CAST([ExecutionTimeMs] AS VARCHAR(20)) + '' ms''
        WHEN [ExecutionTimeMs] < 60000 THEN CAST([ExecutionTimeMs] / 1000.0 AS VARCHAR(20)) + '' sec''
        ELSE CAST([ExecutionTimeMs] / 60000.0 AS VARCHAR(20)) + '' min''
    END AS [ExecutionTimeFormatted]
FROM [dbo].[DatabaseMigrations]
');

PRINT '   ✓ Created vw_MigrationStatus view'

-- Insert initial record for this script
PRINT ''
PRINT '4. Recording this migration...'

DECLARE @ScriptHash NVARCHAR(64) = 'MIGRATION_TRACKING_SETUP_V1_0';
DECLARE @ScriptName NVARCHAR(255) = 'Add_DatabaseMigrations_Table.sql';

IF NOT EXISTS (SELECT 1 FROM [dbo].[DatabaseMigrations] WHERE [ScriptName] = @ScriptName)
BEGIN
    INSERT INTO [dbo].[DatabaseMigrations] 
    ([ScriptName], [Status], [ExecutionTimeMs], [ScriptHash], [AppliedBy], [ScriptCategory], [Notes])
    VALUES 
    (@ScriptName, 'Success', 0, @ScriptHash, 'System', 'Schema', 'Initial migration tracking table setup');
    
    PRINT '   ✓ Recorded migration tracking setup'
END
ELSE
BEGIN
    PRINT '   ✓ Migration already recorded'
END

-- Display summary
PRINT ''
PRINT '5. Setup Summary:'
PRINT '=================='

SELECT 
    'DatabaseMigrations Table' as [Component],
    CASE WHEN EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]')) 
         THEN 'Created' ELSE 'Missing' END as [Status]
UNION ALL
SELECT 
    'Migration Status View' as [Component],
    CASE WHEN EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_MigrationStatus]')) 
         THEN 'Created' ELSE 'Missing' END as [Status]
UNION ALL
SELECT 
    'Performance Indexes' as [Component],
    CASE WHEN EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[DatabaseMigrations]') AND name = N'IX_DatabaseMigrations_AppliedDate')
         THEN 'Created' ELSE 'Missing' END as [Status];

PRINT ''
PRINT '✅ Database Migration Tracking System setup completed successfully!'
PRINT 'You can now use the DatabaseMigrationService to manage your database migrations.'
PRINT ''
