using Microsoft.AspNetCore.Components;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Components.Email
{
    public partial class EmailTemplateSelectionModal : ComponentBase
    {
        // Parameters
        [Parameter] public bool IsVisible { get; set; }
        [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
        [Parameter] public string Title { get; set; } = "Select Email Template";
        [Parameter] public string SelectedTemplateName { get; set; } = "Select Email Template";
        [Parameter] public List<EmailTemplate> EmailTemplates { get; set; } = new();
        [Parameter] public bool IsLoading { get; set; } = false;
        [Parameter] public EventCallback<string> OnTemplateSelected { get; set; }
        [Parameter] public EventCallback OnCancel { get; set; }

        private List<string> GetTemplateNames()
        {
            return EmailTemplates.Select(t => t.Name).ToList();
        }

        private async Task OnSendTemplate()
        {
            if (!string.IsNullOrEmpty(SelectedTemplateName))
            {
                await OnTemplateSelected.InvokeAsync(SelectedTemplateName);
            }
        }

        private async Task HandleCancel()
        {
            SelectedTemplateName = string.Empty;
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
            await OnCancel.InvokeAsync();
        }
    }
}
