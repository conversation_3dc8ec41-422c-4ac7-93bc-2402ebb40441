USE [MusicSchool]
GO

-- Add sidebar theme configuration to UI group (300)
-- Check if the configuration already exists before inserting
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 300 AND [Key] = 'SidebarTheme')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [CreatedUTC])
    VALUES (300, 'SidebarTheme', '1', 'Sidebar color theme selection', 'int', GETUTCDATE());
    
    PRINT 'Sidebar theme configuration added successfully.';
END
ELSE
BEGIN
    PRINT 'Sidebar theme configuration already exists.';
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 300 AND [Key] = 'CustomSidebarColor1')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [Visible], [CreatedUTC])
    VALUES (300, 'CustomSidebarColor1', '#6c5ce7', 'Custom sidebar primary color (hex format)', 'string', 0, GETUTCDATE());
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 300 AND [Key] = 'CustomSidebarColor2')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [Visible], [CreatedUTC])
    VALUES (300, 'CustomSidebarColor2', '#a29bfe', 'Custom sidebar secondary color (hex format)', 'string', 0, GETUTCDATE());
END

-- Display current UI configurations
SELECT [ConfigId], [GroupId], [Key], [Value], [Description], [DataType], [CreatedUTC], [UpdatedUTC]
FROM [dbo].[Config] 
WHERE [GroupId] = 300
ORDER BY [Key];

GO
