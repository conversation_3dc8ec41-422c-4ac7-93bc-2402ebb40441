using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffTutorsService : IBffTutorsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffTutorsService> _logger;
        private readonly string _baseUrl;

        public BffTutorsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffTutorsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<Tutor>> GetTutorsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching tutors from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/tutors");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tutors = JsonSerializer.Deserialize<List<Tutor>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return tutors ?? new List<Tutor>();
                }

                _logger.LogWarning("Failed to fetch tutors. Status: {StatusCode}", response.StatusCode);
                return new List<Tutor>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching tutors from BFF");
                return new List<Tutor>();
            }
        }

        public async Task<Tutor?> GetTutorAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching tutor {TutorId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/tutors/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Tutor>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch tutor {TutorId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching tutor {TutorId} from BFF", id);
                return null;
            }
        }

        public async Task<bool> CreateTutorAsync(Tutor tutor)
        {
            var result = await CreateTutorAsyncInternal(tutor);
            return result != null;
        }

        public async Task<bool> UpdateTutorAsync(Tutor tutor)
        {
            return await UpdateTutorAsyncInternal(tutor.TutorId, tutor);
        }

        private async Task<Tutor?> CreateTutorAsyncInternal(Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Creating tutor via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/tutors");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(tutor);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Tutor>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create tutor. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tutor via BFF");
                return null;
            }
        }

        private async Task<bool> UpdateTutorAsyncInternal(int id, Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Updating tutor {TutorId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/tutors/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(tutor);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update tutor {TutorId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteTutorAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting tutor {TutorId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/tutors/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete tutor {TutorId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tutor {TutorId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> ArchiveTutorAsync(int id)
        {
            try
            {
                _logger.LogInformation("Archiving tutor {TutorId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/tutors/{id}/archive");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} archived successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to archive tutor {TutorId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving tutor {TutorId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> RestoreTutorAsync(int id)
        {
            try
            {
                _logger.LogInformation("Restoring tutor {TutorId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/tutors/{id}/restore");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} restored successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to restore tutor {TutorId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring tutor {TutorId} via BFF", id);
                return false;
            }
        }

        public async Task<List<Tutor>> GetTutorsBySubjectAsync(int subjectId)
        {
            try
            {
                _logger.LogInformation("Fetching tutors by subject {SubjectId} from BFF", subjectId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/tutors/by-subject/{subjectId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tutors = JsonSerializer.Deserialize<List<Tutor>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return tutors ?? new List<Tutor>();
                }

                _logger.LogWarning("Failed to fetch tutors by subject {SubjectId}. Status: {StatusCode}", subjectId, response.StatusCode);
                return new List<Tutor>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching tutors by subject {SubjectId} from BFF", subjectId);
                return new List<Tutor>();
            }
        }

        public async Task<bool> UpdateTutorColorAsync(int tutorId, string color)
        {
            try
            {
                _logger.LogInformation("Updating tutor {TutorId} color to {Color} via BFF", tutorId, color);
                var request = new HttpRequestMessage(HttpMethod.Patch, $"{_baseUrl}/tutors/{tutorId}/color");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var colorRequest = new { Color = color };
                var json = JsonSerializer.Serialize(colorRequest);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} color updated successfully", tutorId);
                    return true;
                }

                _logger.LogWarning("Failed to update tutor {TutorId} color. Status: {StatusCode}", tutorId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} color via BFF", tutorId);
                return false;
            }
        }

        public async Task<int> GetRemainingLessonsAsync(int tutorId)
        {
            try
            {
                _logger.LogInformation("Fetching remaining lessons for tutor {TutorId} from BFF", tutorId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/tutors/{tutorId}/remaining-lessons");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var remainingLessons = JsonSerializer.Deserialize<int>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return remainingLessons;
                }

                _logger.LogWarning("Failed to fetch remaining lessons for tutor {TutorId}. Status: {StatusCode}", tutorId, response.StatusCode);
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching remaining lessons for tutor {TutorId} from BFF", tutorId);
                return 0;
            }
        }
    }
}
