@page "/examples/grid-action-button-group"
@using ShiningCMusicApp.Components.Buttons

<h3>GridActionButtonGroup Examples</h3>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Four-Button Layout (Preview, Files, Edit, Delete)</h5>
                </div>
                <div class="card-body">
                    <GridActionButtonGroup 
                        FirstText="Preview"
                        FirstIcon="bi bi-eye"
                        FirstButtonClass="btn-outline-success"
                        FirstTitle="Preview Template"
                        OnFirstClick="HandlePreview"
                        
                        SecondText="Files"
                        SecondIcon="bi bi-paperclip"
                        SecondButtonClass="btn-outline-info"
                        SecondTitle="Manage Attachments"
                        OnSecondClick="HandleFiles"
                        
                        ShowThirdButton="true"
                        ThirdText="Edit"
                        ThirdIcon="bi bi-pencil"
                        ThirdButtonClass="btn-outline-primary"
                        ThirdTitle="Edit Template"
                        OnThirdClick="HandleEdit"
                        
                        ShowFourthButton="true"
                        FourthText="Delete"
                        FourthIcon="bi bi-trash"
                        FourthButtonClass="btn-outline-danger"
                        FourthTitle="Delete Template"
                        OnFourthClick="HandleDelete" />
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Two-Button Layout (Edit/Delete)</h5>
                </div>
                <div class="card-body">
                    <GridActionButtonGroup 
                        FirstText="Edit"
                        FirstIcon="bi bi-pencil"
                        FirstTitle="Edit Item"
                        OnFirstClick="HandleEdit"
                        
                        SecondText="Delete"
                        SecondIcon="bi bi-trash"
                        SecondButtonClass="btn-outline-danger"
                        SecondTitle="Delete Item"
                        OnSecondClick="HandleDelete" />
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Icons Only (No Labels)</h5>
                </div>
                <div class="card-body">
                    <GridActionButtonGroup 
                        ShowActionButtonLabel="false"
                        FirstIcon="bi bi-eye"
                        FirstTitle="View Details"
                        OnFirstClick="HandleView"
                        
                        SecondIcon="bi bi-pencil"
                        SecondTitle="Edit Item"
                        OnSecondClick="HandleEdit"
                        
                        ShowThirdButton="true"
                        ThirdIcon="bi bi-trash"
                        ThirdButtonClass="btn-outline-danger"
                        ThirdTitle="Delete Item"
                        OnThirdClick="HandleDelete" />
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Custom Styling</h5>
                </div>
                <div class="card-body">
                    <GridActionButtonGroup 
                        FirstText="Approve"
                        FirstIcon="bi bi-check-circle"
                        FirstButtonClass="btn-outline-success"
                        FirstTitle="Approve Request"
                        OnFirstClick="HandleApprove"
                        
                        SecondText="Reject"
                        SecondIcon="bi bi-x-circle"
                        SecondButtonClass="btn-outline-danger"
                        SecondTitle="Reject Request"
                        OnSecondClick="HandleReject"
                        
                        ButtonSize="btn-xs"
                        ContainerClass="d-flex gap-2 justify-content-center" />
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>In Table Context</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sample Item 1</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <GridActionButtonGroup 
                                        FirstIcon="bi bi-eye"
                                        FirstTitle="View"
                                        OnFirstClick="HandleView"
                                        
                                        SecondIcon="bi bi-pencil"
                                        SecondTitle="Edit"
                                        OnSecondClick="HandleEdit"
                                        
                                        ShowThirdButton="true"
                                        ThirdIcon="bi bi-trash"
                                        ThirdButtonClass="btn-outline-danger"
                                        ThirdTitle="Delete"
                                        OnThirdClick="HandleDelete"
                                        
                                        ShowActionButtonLabel="false" />
                                </td>
                            </tr>
                            <tr>
                                <td>Sample Item 2</td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <GridActionButtonGroup 
                                        FirstIcon="bi bi-eye"
                                        FirstTitle="View"
                                        OnFirstClick="HandleView"
                                        
                                        SecondIcon="bi bi-pencil"
                                        SecondTitle="Edit"
                                        OnSecondClick="HandleEdit"
                                        
                                        ShowThirdButton="true"
                                        ThirdIcon="bi bi-trash"
                                        ThirdButtonClass="btn-outline-danger"
                                        ThirdTitle="Delete"
                                        OnThirdClick="HandleDelete"
                                        
                                        ShowActionButtonLabel="false" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void HandlePreview()
    {
        // Preview logic
    }

    private void HandleFiles()
    {
        // Files management logic
    }

    private void HandleView()
    {
        // View logic
    }

    private void HandleEdit()
    {
        // Edit logic
    }

    private void HandleDelete()
    {
        // Delete logic
    }

    private void HandleApprove()
    {
        // Approve logic
    }

    private void HandleReject()
    {
        // Reject logic
    }
}
