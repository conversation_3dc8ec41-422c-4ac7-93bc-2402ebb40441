# PowerShell script to rename SQL files with numeric prefixes
# This script will rename all SQL files in the SQL directory to have script_XXX_ prefixes

param(
    [string]$SqlPath = "../ShiningCMusicApi/SQL",
    [switch]$WhatIf = $false
)

Write-Host "Renaming SQL scripts with numeric prefixes..." -ForegroundColor Cyan
Write-Host "SQL Path: $SqlPath" -ForegroundColor Gray
Write-Host "WhatIf Mode: $WhatIf" -ForegroundColor Gray
Write-Host ""

# Define the desired order for SQL scripts
$scriptOrder = @(
    # Core database creation (001-010)
    @{ Pattern = "MusicSchoolDB Create.sql"; NewNumber = 1; Description = "Database creation script" },
    @{ Pattern = "MusicSchoolDB Objects.sql"; NewNumber = 2; Description = "Core database objects" },
    @{ Pattern = "Add_DatabaseMigrations_Table.sql"; NewNumber = 3; Description = "Migration tracking table" },
    
    # Authentication and security (011-020)
    @{ Pattern = "Add_OpenIddict_Tables.sql"; NewNumber = 11; Description = "OpenIddict authentication tables" },
    @{ Pattern = "Add_ClientSecrets_Table.sql"; NewNumber = 12; Description = "Client secrets table" },
    @{ Pattern = "Migrate_Clients_To_OpenIddict.sql"; NewNumber = 13; Description = "Client migration to OpenIddict" },
    
    # Core application tables (021-050)
    @{ Pattern = "Lessons Table.sql"; NewNumber = 21; Description = "Lessons table creation" },
    @{ Pattern = "Add_Timesheet_Tables.sql"; NewNumber = 22; Description = "Timesheet tables" },
    @{ Pattern = "Add_Config_Table.sql"; NewNumber = 23; Description = "Configuration table" },
    @{ Pattern = "Add_EmailQueue_And_Config.sql"; NewNumber = 24; Description = "Email queue and configuration" },
    @{ Pattern = "Add_Email_Templates.sql"; NewNumber = 25; Description = "Email templates" },
    @{ Pattern = "Add_EmailQueueAttachments.sql"; NewNumber = 26; Description = "Email queue attachments" },
    
    # Field additions and modifications (051-100)
    @{ Pattern = "Add_Fee_Field.sql"; NewNumber = 51; Description = "Add fee field" },
    @{ Pattern = "Add_RemainingLessons_Field.sql"; NewNumber = 52; Description = "Add remaining lessons field" },
    @{ Pattern = "Add_ExcludeEmail_Field.sql"; NewNumber = 53; Description = "Add exclude email field" },
    @{ Pattern = "Add_Config_Visible_Column.sql"; NewNumber = 54; Description = "Add config visible column" },
    @{ Pattern = "Add_Recurring_Events_Support.sql"; NewNumber = 55; Description = "Add recurring events support" },
    
    # Updates and modifications (101-150)
    @{ Pattern = "UpdatePasswordFieldLength.sql"; NewNumber = 101; Description = "Update password field length" },
    @{ Pattern = "Update_TimesheetEntry_RecordId.sql"; NewNumber = 102; Description = "Update timesheet entry record ID" },
    @{ Pattern = "Update_TimesheetEntry_Signature_Field.sql"; NewNumber = 103; Description = "Update timesheet entry signature field" },
    
    # Configuration additions (151-200)
    @{ Pattern = "Add_PaymentReminder_Template.sql"; NewNumber = 151; Description = "Add payment reminder template" },
    @{ Pattern = "Add_RemainingLessonsUpdate_Config.sql"; NewNumber = 152; Description = "Add remaining lessons update config" },
    @{ Pattern = "Add_Sidebar_Theme_Config.sql"; NewNumber = 153; Description = "Add sidebar theme config" },
    @{ Pattern = "Add_TimesheetCleanup_Config.sql"; NewNumber = 154; Description = "Add timesheet cleanup config" },
    
    # Stored procedures (201-250)
    @{ Pattern = "Add_sp_GetStudents.sql"; NewNumber = 201; Description = "Add GetStudents stored procedure" },
    
    # Access level migrations (251-300)
    @{ Pattern = "AddAccessLevelToUserRoles_Fixed.sql"; NewNumber = 251; Description = "Add access level to user roles (fixed)" },
    @{ Pattern = "AddAccessLevelToUserRoles_StepByStep.sql"; NewNumber = 252; Description = "Add access level to user roles (step by step)" },
    
    # Sample data (always last - 900+)
    @{ Pattern = "Sample Data.sql"; NewNumber = 901; Description = "Sample data insertion" }
)

# Get all SQL files in the directory
if (-not (Test-Path $SqlPath)) {
    Write-Error "SQL path does not exist: $SqlPath"
    exit 1
}

$sqlFiles = Get-ChildItem -Path $SqlPath -Filter "*.sql" | Where-Object { 
    -not $_.Name.StartsWith("Test_") -and 
    -not $_.Name.StartsWith("Verify") -and 
    -not $_.Name.StartsWith("Rollback") -and
    -not $_.Name.StartsWith("script_")
}

Write-Host "Found $($sqlFiles.Count) SQL files to rename:" -ForegroundColor Green
foreach ($file in $sqlFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor Gray
}
Write-Host ""

# Process each file according to the defined order
$renamedCount = 0
$skippedFiles = @()

foreach ($orderItem in $scriptOrder) {
    $matchingFile = $sqlFiles | Where-Object { $_.Name -eq $orderItem.Pattern }
    
    if ($matchingFile) {
        $newName = "script_{0:D3}_{1}" -f $orderItem.NewNumber, $orderItem.Pattern.Replace(" ", "_").Replace(".sql", ".sql")
        $newPath = Join-Path $SqlPath $newName
        
        Write-Host "Renaming: $($matchingFile.Name)" -ForegroundColor Yellow
        Write-Host "      To: $newName" -ForegroundColor Green
        Write-Host "    Desc: $($orderItem.Description)" -ForegroundColor Gray
        
        if (-not $WhatIf) {
            try {
                Rename-Item -Path $matchingFile.FullName -NewName $newName -ErrorAction Stop
                $renamedCount++
                Write-Host "    ✓ Renamed successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "    ✗ Failed to rename: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        else {
            Write-Host "    (WhatIf mode - no actual rename)" -ForegroundColor Cyan
            $renamedCount++
        }
        Write-Host ""
    }
}

# Handle any remaining files that weren't in the predefined order
$remainingFiles = $sqlFiles | Where-Object { 
    $fileName = $_.Name
    -not ($scriptOrder | Where-Object { $_.Pattern -eq $fileName })
}

if ($remainingFiles.Count -gt 0) {
    Write-Host "Processing remaining files (starting from script_800):" -ForegroundColor Yellow
    $nextNumber = 800
    
    foreach ($file in $remainingFiles) {
        $newName = "script_{0:D3}_{1}" -f $nextNumber, $file.Name.Replace(" ", "_")
        $newPath = Join-Path $SqlPath $newName
        
        Write-Host "Renaming: $($file.Name)" -ForegroundColor Yellow
        Write-Host "      To: $newName" -ForegroundColor Green
        Write-Host "    Desc: Unclassified script" -ForegroundColor Gray
        
        if (-not $WhatIf) {
            try {
                Rename-Item -Path $file.FullName -NewName $newName -ErrorAction Stop
                $renamedCount++
                Write-Host "    ✓ Renamed successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "    ✗ Failed to rename: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        else {
            Write-Host "    (WhatIf mode - no actual rename)" -ForegroundColor Cyan
            $renamedCount++
        }
        
        $nextNumber++
        Write-Host ""
    }
}

# Summary
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "Total files processed: $renamedCount" -ForegroundColor Green
Write-Host "WhatIf mode: $WhatIf" -ForegroundColor Gray

if ($WhatIf) {
    Write-Host ""
    Write-Host "To actually rename the files, run:" -ForegroundColor Yellow
    Write-Host "  .\rename-sql-scripts.ps1 -WhatIf:`$false" -ForegroundColor White
}
else {
    Write-Host ""
    Write-Host "All SQL scripts have been renamed with numeric prefixes!" -ForegroundColor Green
    Write-Host "The Database Manager will now process them in the correct order." -ForegroundColor Green
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Copy the renamed SQL files to the ShiningCMusicDbManager/SQL directory" -ForegroundColor White
Write-Host "2. Run the Database Manager to apply migrations" -ForegroundColor White
Write-Host "3. Test the migration process" -ForegroundColor White
