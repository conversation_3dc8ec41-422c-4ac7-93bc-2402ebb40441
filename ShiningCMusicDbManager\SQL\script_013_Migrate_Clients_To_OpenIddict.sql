-- Migrate existing IdentityServer4 clients to OpenIddict format
-- This script migrates data from Clients, ClientScopes, and ClientGrantTypes tables to OpenIddict tables

-- First, ensure OpenIddict tables exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OpenIddictApplications' AND xtype='U')
BEGIN
    PRINT 'Error: OpenIddict tables do not exist. Please run Add_OpenIddict_Tables.sql first.'
    RETURN
END

-- Clear existing OpenIddict data (for clean migration)
DELETE FROM [OpenIddictTokens]
DELETE FROM [OpenIddictAuthorizations]
DELETE FROM [OpenIddictApplications]
DELETE FROM [OpenIddictScopes]

PRINT 'Cleared existing OpenIddict data'

-- Create scopes first
INSERT INTO [OpenIddictScopes] ([Id], [Name], [DisplayName], [Description], [Resources])
VALUES 
    (NEWID(), 'ShiningCMusicApi', 'Shining C Music API', 'Access to Shining C Music API', '["ShiningCMusicApi"]'),
    (NEWID(), 'openid', 'OpenID', 'OpenID Connect scope', '[]'),
    (NEWID(), 'profile', 'Profile', 'User profile information', '[]')

PRINT 'Created OpenIddict scopes'

-- Migrate clients from IdentityServer4 to OpenIddict
INSERT INTO [OpenIddictApplications] (
    [Id],
    [ClientId], 
    [ClientSecret],
    [DisplayName],
    [ApplicationType],
    [ClientType],
    [ConsentType],
    [Permissions],
    [RedirectUris],
    [PostLogoutRedirectUris]
)
SELECT 
    NEWID() as [Id],
    c.[ClientId],
    c.[ClientSecret],
    c.[ClientId] as [DisplayName],
    'web' as [ApplicationType],
    CASE 
        WHEN EXISTS (SELECT 1 FROM [ClientGrantTypes] cgt WHERE cgt.[ClientId] = c.[ClientId] AND cgt.[GrantType] = 'client_credentials')
        THEN 'confidential'
        ELSE 'public'
    END as [ClientType],
    'explicit' as [ConsentType],
    -- Build permissions JSON based on grant types and scopes
    '[' + 
    CASE 
        WHEN EXISTS (SELECT 1 FROM [ClientGrantTypes] cgt WHERE cgt.[ClientId] = c.[ClientId] AND cgt.[GrantType] = 'client_credentials')
        THEN '"ept:token","gt:client_credentials"'
        ELSE '"ept:authorization","ept:token","gt:authorization_code"'
    END +
    -- Add scope permissions
    CASE 
        WHEN EXISTS (SELECT 1 FROM [ClientScopes] cs WHERE cs.[ClientId] = c.[ClientId])
        THEN ',' + STUFF((
            SELECT ',"scp:' + cs.[Scope] + '"'
            FROM [ClientScopes] cs 
            WHERE cs.[ClientId] = c.[ClientId]
            FOR XML PATH('')
        ), 1, 1, '')
        ELSE ''
    END +
    ']' as [Permissions],
    '[]' as [RedirectUris], -- Will be updated for authorization code flow clients
    '[]' as [PostLogoutRedirectUris]
FROM [Clients] c

PRINT 'Migrated ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' clients to OpenIddict'

-- Update specific clients for authorization code flow (for future BFF implementation)
UPDATE [OpenIddictApplications] 
SET 
    [RedirectUris] = '["https://localhost:7091/signin-oidc"]',
    [PostLogoutRedirectUris] = '["https://localhost:7091/signout-callback-oidc"]',
    [Permissions] = REPLACE([Permissions], '"gt:client_credentials"', '"gt:authorization_code"')
WHERE [ClientId] = 'wasm_client'

PRINT 'Updated wasm_client for authorization code flow'

-- Verify migration
SELECT 
    'IdentityServer4 Clients' as [Source],
    COUNT(*) as [Count]
FROM [Clients]
UNION ALL
SELECT 
    'OpenIddict Applications' as [Source],
    COUNT(*) as [Count]
FROM [OpenIddictApplications]

PRINT 'Migration completed successfully!'
PRINT 'Note: Both IdentityServer4 and OpenIddict tables now contain client data.'
PRINT 'After testing OpenIddict, you can remove the IdentityServer4 tables.'
