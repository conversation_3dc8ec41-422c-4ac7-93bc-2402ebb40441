@page "/tutors"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Components.Buttons
@using ShiningCMusicApp.Components.Dialogs
@using ShiningCMusicApp.Components.Email
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@attribute [RequireLevel80Access]
@inherits TutorsBase

<PageTitle>Tutor Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎓 <span class="d-none d-sm-inline">Tutor Management</span><span class="d-sm-none">Tutors</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading tutors...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Tutors</h5>
                            <PageActionButtonGroup ThirdTextDesktop="Add New Tutor"
                                                   ThirdTextMobile="Add Tutor"
                                                   ThirdIcon="bi bi-person-add"
                                                   OnThirdClick="OpenCreateModal"
                                                   FourthText="Refresh"
                                                   FourthIcon="bi bi-arrow-clockwise"
                                                   OnFourthClick="RefreshData" />
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@tutors" 
                                AllowPaging="true"
                                AllowSorting="true"
                                AllowFiltering="true"
                                AllowResizing="true"
                                AllowMultiSorting="false"
                                EnableAdaptiveUI="true"
                                AdaptiveUIMode="AdaptiveMode.Mobile"
                                GridLines="GridLine.Both"
                                Height="600" 
                                CssClass="mobile-grid">
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(Tutor.CreatedUTC)" Direction="SortDirection.Descending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Tutor.TutorId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.TutorName) HeaderText="Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Email) HeaderText="Email" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.SubjectName) HeaderText="Subject" Width="100"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.LoginName) HeaderText="Login Name" Width="100"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Color) HeaderText="Color" Width="100">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <div class="d-flex align-items-center">
                                            <div class="tutor-color-circle" style="background-color: @tutor?.Color;"></div>
                                            <span class="d-none d-sm-inline">@tutor?.Color</span>
                                        </div>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Tutor.CreatedUTC) HeaderText="Created" Width="100" Format="dd/MM/yyyy"
                                            AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <GridActionButtonGroup ShowFirstButton="true"
                                                               FirstText="Email"
                                                               FirstIcon="bi bi-envelope"
                                                               FirstTitle="Send Schedule Email"
                                                               IsFirstDisabled="@IsEmailButtonDisabled(tutor)"
                                                               OnFirstClick="() => SendScheduleEmail(tutor, scheduleReadyTemplate)"
                                                               ThirdText="Edit"
                                                               ThirdIcon="bi bi-pencil"
                                                               ThirdTitle="Edit Tutor"
                                                               OnThirdClick="() => OpenEditModal(tutor)"
                                                               FourthText="Delete"
                                                               FourthIcon="bi bi-trash"
                                                               FourthTitle="Delete Tutor"
                                                               OnFourthClick="() => DeleteTutor(tutor)"
                                                               ShowActionButtonLabel="@ShowActionButtonLabel"
                                                               ButtonBaseClass="grid-action-btn grid-btn-third" />
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTutor" OnValidSubmit="@SaveTutor">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Tutor Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentTutor.TutorName" Placeholder="Enter tutor name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.TutorName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentTutor.Email" Placeholder="Enter email address" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.Email)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Subject @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentTutorSubjectName" Readonly="true"
                                   CssClass="form-control" Placeholder="No subject assigned"></SfTextBox>
                        <small class="form-text text-muted">Subject cannot be changed when editing. Create a new tutor to assign a different subject.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Subject" @bind-Value="currentTutor.SubjectId"
                                        DataSource="@subjects" CssClass="form-control" Placeholder="Select a subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Subject">
                                <ItemTemplate Context="subjectItem">
                                    <span>@((subjectItem as Subject)?.SubjectName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a subject for this student (required)</small>
                        @if (showSubjectValidation)
                        {
                            <div class="text-danger">Subject is required.</div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentTutor.LoginName" Placeholder="Assigned via user management"
                               CssClass="form-control" Readonly="true"></SfTextBox>
                    <small class="form-text text-muted">Login name is managed through the Admin page user assignment</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">Color</label>
                    <div class="d-flex align-items-center gap-2">
                        <SfColorPicker Value="@(currentTutor.Color ?? "#6C757D")"
                                       ShowButtons="true"
                                       Mode="ColorPickerMode.Picker"
                                       ModeSwitcher="false"
                                       ValueChange="@((ColorPickerEventArgs args) => OnCurrentTutorColorChanged(args.CurrentValue.Hex))"
                                       CssClass="me-2">
                        </SfColorPicker>
                        <SfTextBox @bind-Value="currentTutor.Color"
                                   Placeholder="#6C757D"
                                   CssClass="form-control"
                                   style="max-width: 120px;">
                        </SfTextBox>
                    </div>
                    <small class="form-text text-muted">Choose a color to identify this tutor in the schedule</small>
                    <ValidationMessage For="@(() => currentTutor.Color)" />
                </div>
                <ModalActionButtonGroup FirstText="@(isEditMode ? "Update" : "Create")"
                                        FirstIcon="@(isEditMode ? "bi bi-pencil" : "bi bi-plus-circle")"
                                        FirstButtonType="submit"
                                        IsLoading="@isSaving"
                                        IsFirstDisabled="@isSaving"
                                        SecondText="Cancel"
                                        OnSecondClick="CloseModal" />
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Template Selection Modal -->
<EmailTemplateSelectionModal @bind-IsVisible="showTemplateSelectionModal"
                             Title="@templateSelectionModalTitle"
                             EmailTemplates="@emailTemplates"
                             SelectedTemplateName="@selectedTemplateName"
                             IsLoading="@isSendingEmail"
                             OnTemplateSelected="@SendSelectedTemplate"
                             OnCancel="@CloseTemplateSelectionModal" />

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />
