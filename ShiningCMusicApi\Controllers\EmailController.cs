using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailController : ControllerBase
    {
        private readonly IEmailService _emailService;
        private readonly ITutorService _tutorService;
        private readonly ILogger<EmailController> _logger;

        public EmailController(
            IEmailService emailService,
            ITutorService tutorService,
            ILogger<EmailController> logger)
        {
            _emailService = emailService;
            _tutorService = tutorService;
            _logger = logger;
        }

        // POST: api/email/send-schedule-ready/{tutorId}
        [HttpPost("send-schedule-ready/{tutorId}")]
        public async Task<IActionResult> SendScheduleReadyEmail(int tutorId)
        {
            try
            {
                var tutor = await _tutorService.GetTutorAsync(tutorId);
                if (tutor == null)
                {
                    return NotFound(new { message = "Tutor not found" });
                }

                if (string.IsNullOrWhiteSpace(tutor.Email))
                {
                    return BadRequest(new { message = "Tutor does not have an email address" });
                }

                var success = await _emailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName ?? "Tutor");
                if (success)
                {
                    return Ok(new { message = $"Schedule ready email sent to {tutor.TutorName}" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to send email" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending schedule ready email");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send
        [HttpPost("send")]
        public async Task<IActionResult> SendEmail([FromBody] SendEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ToEmail))
                {
                    return BadRequest(new { message = "Recipient email is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Subject))
                {
                    return BadRequest(new { message = "Email subject is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Body))
                {
                    return BadRequest(new { message = "Email body is required" });
                }

                var success = await _emailService.SendEmailAsync(
                    request.ToEmail,
                    request.Subject,
                    request.Body,
                    request.IsHtml);

                if (success)
                {
                    return Ok(new { message = $"Email sent to {request.ToEmail}" });
                }
                else
                {
                    return StatusCode(500, new { message = "Failed to send email" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send-template
        [HttpPost("send-template")]
        public async Task<IActionResult> SendTemplateEmail([FromBody] SendTemplateEmailRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get all placeholders from the recipient (typed + properties + additional)
                var placeholders = request.Recipient.GetAllPlaceholders();

                // Add additional placeholders (these can override existing ones)
                if (request.AdditionalPlaceholders != null)
                {
                    foreach (var kvp in request.AdditionalPlaceholders)
                        placeholders[kvp.Key] = kvp.Value;
                }

                var success = await _emailService.SendEmailFromTemplateAsync(
                    request.TemplateName,
                    request.Recipient.Email,
                    placeholders);

                return success
                    ? Ok(new { message = $"Email sent to {request.Recipient.Name} using template '{request.TemplateName}'" })
                    : StatusCode(500, new { message = "Failed to send email" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending template email to {Email}", request.Recipient?.Email);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send-template/bulk
        [HttpPost("send-template/bulk")]
        public async Task<IActionResult> SendBulkTemplateEmail([FromBody] BulkTemplateEmailRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var results = new List<EmailSendResult>();
                var successCount = 0;
                var failureCount = 0;

                foreach (var recipient in request.Recipients)
                {
                    try
                    {
                        // Get all placeholders from the recipient (typed + properties)
                        var placeholders = recipient.GetAllPlaceholders();

                        // Add global placeholders (these can override existing ones)
                        if (request.GlobalPlaceholders != null)
                        {
                            foreach (var kvp in request.GlobalPlaceholders)
                                placeholders[kvp.Key] = kvp.Value;
                        }

                        var success = await _emailService.SendEmailFromTemplateAsync(
                            request.TemplateName,
                            recipient.Email,
                            placeholders);

                        if (success)
                        {
                            successCount++;
                            results.Add(new EmailSendResult
                            {
                                Email = recipient.Email,
                                Name = recipient.Name,
                                Success = true
                            });
                        }
                        else
                        {
                            failureCount++;
                            results.Add(new EmailSendResult
                            {
                                Email = recipient.Email,
                                Name = recipient.Name,
                                Success = false,
                                ErrorMessage = "Failed to send email"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        results.Add(new EmailSendResult
                        {
                            Email = recipient.Email,
                            Name = recipient.Name,
                            Success = false,
                            ErrorMessage = ex.Message
                        });
                        _logger.LogError(ex, "Error sending email to {Email}", recipient.Email);
                    }
                }

                var response = new BulkEmailSendResponse
                {
                    TotalRecipients = request.Recipients.Count,
                    SuccessCount = successCount,
                    FailureCount = failureCount,
                    Results = results,
                    Message = $"Bulk email completed: {successCount} successful, {failureCount} failed"
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing bulk template email request");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: api/email/send-direct
        [HttpPost("send-direct")]
        public async Task<IActionResult> SendDirectEmail([FromBody] DirectEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ToEmail))
                {
                    return BadRequest(new { message = "ToEmail is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Subject))
                {
                    return BadRequest(new { message = "Subject is required" });
                }

                if (string.IsNullOrWhiteSpace(request.BodyText) && string.IsNullOrWhiteSpace(request.BodyHtml))
                {
                    return BadRequest(new { message = "Either BodyText or BodyHtml is required" });
                }

                if (request.SendImmediately)
                {
                    // Send immediately via SMTP (bypass queue)
                    var body = request.BodyHtml ?? request.BodyText ?? "";
                    var isHtml = !string.IsNullOrWhiteSpace(request.BodyHtml);

                    // Create a temporary EmailQueue object for direct sending
                    var emailItem = new EmailQueue
                    {
                        ToEmail = request.ToEmail,
                        CC = request.CC,
                        BCC = request.BCC,
                        Subject = request.Subject,
                        BodyText = isHtml ? null : body,
                        BodyHtml = isHtml ? body : null,
                        Attachments = request.Attachments?.Select(a => new EmailQueueAttachment
                        {
                            AttachmentName = a.AttachmentName,
                            AttachmentPath = a.AttachmentPath,
                            ContentType = a.ContentType,
                            FileSizeBytes = a.FileSizeBytes
                        }).ToList() ?? new List<EmailQueueAttachment>()
                    };

                    var success = await _emailService.SendEmailDirectlyAsync(emailItem);
                    if (success)
                    {
                        return Ok(new { message = "Email sent immediately", method = "direct" });
                    }
                    else
                    {
                        return StatusCode(500, new { message = "Failed to send email directly" });
                    }
                }
                else
                {
                    // Queue the email (default behavior)
                    var emailQueueService = HttpContext.RequestServices.GetRequiredService<IEmailQueueService>();
                    var body = request.BodyHtml ?? request.BodyText ?? "";
                    var isHtml = !string.IsNullOrWhiteSpace(request.BodyHtml);

                    var emailId = await emailQueueService.QueueEmailAsync(
                        request.ToEmail,
                        request.Subject,
                        body,
                        isHtml,
                        request.CC,
                        request.BCC,
                        null, // deliver immediately
                        request.Attachments
                    );

                    return Ok(new {
                        message = "Email queued successfully",
                        emailId,
                        method = "queued",
                        note = "Email will be processed by the background service"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending direct email to {ToEmail}", request.ToEmail);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }

    public class SendEmailRequest
    {
        public string ToEmail { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = true;
    }
}
