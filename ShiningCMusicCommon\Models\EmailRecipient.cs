using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class EmailRecipient
    {
        [Required]
        [EmailAddress]
        [StringLength(256)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(256)]
        public string Name { get; set; } = string.Empty;

        // Use typed placeholders with fallback to dictionary
        public EmailPlaceholderBase? Placeholders { get; set; }

        // Fallback for backward compatibility or simple scenarios
        public Dictionary<string, string>? Properties { get; set; }

        // Helper method to get all placeholders as dictionary
        public Dictionary<string, string> GetAllPlaceholders()
        {
            var result = new Dictionary<string, string>();

            // Start with typed placeholders if available
            if (Placeholders != null)
            {
                result = Placeholders.ToDictionary();
            }

            // Add/override with properties dictionary
            if (Properties != null)
            {
                foreach (var kvp in Properties)
                {
                    result[kvp.Key] = kvp.Value;
                }
            }

            // Ensure basic placeholders exist
            if (!result.ContainsKey("RecipientName"))
                result["RecipientName"] = Name;
            if (!result.ContainsKey("RecipientEmail"))
                result["RecipientEmail"] = Email;

            return result;
        }
    }

    public class SendTemplateEmailRequest
    {
        [Required]
        [StringLength(50)]
        public string TemplateName { get; set; } = string.Empty;

        [Required]
        public EmailRecipient Recipient { get; set; } = new();

        public Dictionary<string, string>? AdditionalPlaceholders { get; set; }
    }

    public class BulkTemplateEmailRequest
    {
        [Required]
        [StringLength(50)]
        public string TemplateName { get; set; } = string.Empty;

        [Required]
        [MinLength(1, ErrorMessage = "At least one recipient is required")]
        [MaxLength(100, ErrorMessage = "Maximum 100 recipients allowed per request")]
        public List<EmailRecipient> Recipients { get; set; } = new();

        public Dictionary<string, string>? GlobalPlaceholders { get; set; }
    }

    public class EmailSendResult
    {
        public string Email { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class BulkEmailSendResponse
    {
        public int TotalRecipients { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<EmailSendResult> Results { get; set; } = new();
        public string Message { get; set; } = string.Empty;
    }
}
