using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Buttons
{
    public partial class ModalActionButtonGroup : ComponentBase
    {
        // First Button Parameters
        [Parameter] public bool ShowFirstButton { get; set; } = true;
        [Parameter] public string FirstText { get; set; } = "Save";
        [Parameter] public string FirstIcon { get; set; } = "bi bi-check-circle";
        [Parameter] public string FirstIconColor { get; set; } = "white";
        [Parameter] public string FirstButtonClass { get; set; } = "btn-blue-custom";
        [Parameter] public string FirstButtonType { get; set; } = "button";
        [Parameter] public bool IsFirstDisabled { get; set; } = false;
        [Parameter] public EventCallback OnFirstClick { get; set; }

        // Second Button Parameters
        [Parameter] public bool ShowSecondButton { get; set; } = true;
        [Parameter] public string SecondText { get; set; } = "Cancel";
        [Parameter] public string SecondIcon { get; set; } = "bi bi-x-circle";
        [Parameter] public string SecondIconColor { get; set; } = "inherit";
        [Parameter] public string SecondButtonClass { get; set; } = "btn-cancel-custom";
        [Parameter] public string SecondButtonType { get; set; } = "button";
        [Parameter] public bool IsSecondDisabled { get; set; } = false;
        [Parameter] public EventCallback OnSecondClick { get; set; }

        // Third Button Parameters (optional third button)
        [Parameter] public bool ShowThirdButton { get; set; } = false;
        [Parameter] public string ThirdText { get; set; } = string.Empty;
        [Parameter] public string ThirdIcon { get; set; } = string.Empty;
        [Parameter] public string ThirdIconColor { get; set; } = "white";
        [Parameter] public string ThirdButtonClass { get; set; } = "btn-success";
        [Parameter] public string ThirdButtonType { get; set; } = "button";
        [Parameter] public bool IsThirdDisabled { get; set; } = false;
        [Parameter] public EventCallback OnThirdClick { get; set; }

        // Loading State
        [Parameter] public bool IsLoading { get; set; } = false;

        // Container options
        [Parameter] public bool ShowContainer { get; set; } = true;
        [Parameter] public string ContainerClass { get; set; } = "d-flex justify-content-end gap-2 mt-4";

        private string GetFirstButtonClass()
        {
            return $"btn {FirstButtonClass}";
        }

        private string GetSecondButtonClass()
        {
            return $"btn {SecondButtonClass}";
        }

        private string GetThirdButtonClass()
        {
            return $"btn {ThirdButtonClass}";
        }
    }
}
