# GridActionButtonGroup Component

A reusable Blazor component for creating consistent grid action button groups with responsive labels and conditional visibility. Designed specifically for data grid row actions.

## Features

- **Responsive Labels**: Labels show only on large screens (lg and up)
- **Conditional Visibility**: Show/hide buttons based on permissions or state
- **Bootstrap Integration**: Uses standard Bootstrap button classes with outline styles
- **Flexible Styling**: Customizable button styles and container layout
- **Icon Support**: Bootstrap icon integration with inherit color styling
- **Tooltip Support**: Built-in title attribute support for accessibility

## Basic Usage

### Four-Button Layout (Preview, Files, Edit, Delete)
```razor
<GridActionButtonGroup 
    FirstText="Preview"
    FirstIcon="bi bi-eye"
    FirstButtonClass="btn-outline-success"
    FirstTitle="Preview Template"
    OnFirstClick="() => PreviewTemplate(template)"
    
    SecondText="Files"
    SecondIcon="bi bi-paperclip"
    SecondButtonClass="btn-outline-info"
    SecondTitle="Manage Attachments"
    OnSecondClick="() => ManageAttachments(template)"
    
    ShowThirdButton="true"
    ThirdText="Edit"
    ThirdIcon="bi bi-pencil"
    ThirdButtonClass="btn-outline-primary"
    ThirdTitle="Edit Template"
    OnThirdClick="() => OpenEditModal(template)"
    
    ShowFourthButton="true"
    FourthText="Delete"
    FourthIcon="bi bi-trash"
    FourthButtonClass="btn-outline-danger"
    FourthTitle="Delete Template"
    OnFourthClick="() => DeleteTemplate(template)" />
```

### Two-Button Layout (Edit/Delete)
```razor
<GridActionButtonGroup 
    FirstText="Edit"
    FirstIcon="bi bi-pencil"
    FirstTitle="Edit Item"
    OnFirstClick="() => EditItem(item)"
    
    SecondText="Delete"
    SecondIcon="bi bi-trash"
    SecondButtonClass="btn-outline-danger"
    SecondTitle="Delete Item"
    OnSecondClick="() => DeleteItem(item)" />
```

### Without Labels (Icons Only)
```razor
<GridActionButtonGroup 
    ShowActionButtonLabel="false"
    FirstIcon="bi bi-eye"
    FirstTitle="View Details"
    OnFirstClick="() => ViewDetails(item)"
    
    SecondIcon="bi bi-pencil"
    SecondTitle="Edit Item"
    OnSecondClick="() => EditItem(item)" />
```

## Parameters

### First Button
- `ShowFirstButton` (bool): Show/hide first button (default: true)
- `FirstText` (string): Button text for large screens (default: "")
- `FirstIcon` (string): Bootstrap icon class (default: "")
- `FirstButtonClass` (string): CSS class (default: "btn-outline-primary")
- `FirstTitle` (string): Tooltip text (default: "")
- `IsFirstDisabled` (bool): Disable button (default: false)
- `OnFirstClick` (EventCallback): Click handler

### Second Button
- `ShowSecondButton` (bool): Show/hide second button (default: true)
- `SecondText` (string): Button text for large screens (default: "")
- `SecondIcon` (string): Bootstrap icon class (default: "")
- `SecondButtonClass` (string): CSS class (default: "btn-outline-secondary")
- `SecondTitle` (string): Tooltip text (default: "")
- `IsSecondDisabled` (bool): Disable button (default: false)
- `OnSecondClick` (EventCallback): Click handler

### Third Button
- `ShowThirdButton` (bool): Show/hide third button (default: false)
- `ThirdText` (string): Button text for large screens (default: "")
- `ThirdIcon` (string): Bootstrap icon class (default: "")
- `ThirdButtonClass` (string): CSS class (default: "btn-outline-success")
- `ThirdTitle` (string): Tooltip text (default: "")
- `IsThirdDisabled` (bool): Disable button (default: false)
- `OnThirdClick` (EventCallback): Click handler

### Fourth Button
- `ShowFourthButton` (bool): Show/hide fourth button (default: false)
- `FourthText` (string): Button text for large screens (default: "")
- `FourthIcon` (string): Bootstrap icon class (default: "")
- `FourthButtonClass` (string): CSS class (default: "btn-outline-info")
- `FourthTitle` (string): Tooltip text (default: "")
- `IsFourthDisabled` (bool): Disable button (default: false)
- `OnFourthClick` (EventCallback): Click handler

### General
- `ShowActionButtonLabel` (bool): Show text labels on large screens (default: true)
- `ShowContainer` (bool): Whether to wrap buttons in container div (default: true)
- `ContainerClass` (string): Container CSS classes (default: "d-flex gap-1")
- `ButtonSize` (string): Button size class (default: "btn-sm")
- `ButtonBaseClass` (string): Base CSS classes for all buttons (default: "grid-action-btn grid-btn-fourth")

## Common Patterns

### Permission-Based Visibility
```razor
<GridActionButtonGroup
    ShowFirstButton="@CanView"
    ShowSecondButton="@CanEdit"
    ShowThirdButton="@CanDelete"
    ShowFourthButton="@CanManage"
    ... />
```

### Custom Button Styles
```razor
<GridActionButtonGroup 
    FirstButtonClass="btn-outline-info"
    SecondButtonClass="btn-outline-warning"
    ThirdButtonClass="btn-outline-danger"
    ButtonSize="btn-xs"
    ... />
```

### Without Container
```razor
<td>
    <GridActionButtonGroup
        ShowContainer="false"
        FirstIcon="bi bi-eye"
        SecondIcon="bi bi-pencil"
        OnFirstClick="() => View(item)"
        OnSecondClick="() => Edit(item)" />
</td>
```

## Migration Example

### Before
```razor
<div class="d-flex gap-1">
    <button class="btn btn-outline-success btn-sm grid-action-btn grid-btn-fourth" @onclick="() => PreviewTemplate(template)"
            title="Preview Template">
        <i class="bi bi-eye" style="color: inherit;"></i>
        @if (ShowActionButtonLabel)
        {
            <span class="d-none d-lg-inline ms-1">Preview</span>
        }
    </button>
    <button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-fourth" @onclick="() => OpenEditModal(template)"
            title="Edit Template">
        <i class="bi bi-pencil" style="color: inherit;"></i>
        @if (ShowActionButtonLabel)
        {
            <span class="d-none d-lg-inline ms-1">Edit</span>
        }
    </button>
</div>
```

### After
```razor
<GridActionButtonGroup 
    FirstText="Preview"
    FirstIcon="bi bi-eye"
    FirstButtonClass="btn-outline-success"
    FirstTitle="Preview Template"
    OnFirstClick="() => PreviewTemplate(template)"
    
    SecondText="Edit"
    SecondIcon="bi bi-pencil"
    SecondTitle="Edit Template"
    OnSecondClick="() => OpenEditModal(template)"
    
    ShowActionButtonLabel="@ShowActionButtonLabel" />
```

## Notes

- Labels are hidden on screens smaller than `lg` (1200px) to save space in grid columns
- Icons use `color: inherit` to match the button's text color
- All buttons include the `grid-action-btn grid-btn-fourth` classes by default for consistent styling
- The component is optimized for data grid scenarios where space is limited
- Tooltips (title attributes) provide accessibility when labels are hidden
