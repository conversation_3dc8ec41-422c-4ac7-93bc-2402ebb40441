USE [MusicSchool]
GO

-- Add configuration entries for RemainingLessonsUpdateService
-- Group 200 = BackgroundProcessors

-- Check if RemainingLessonsUpdateEnabled config exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'RemainingLessonsUpdateEnabled')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [Visible])
    VALUES (200, 'RemainingLessonsUpdateEnabled', 'true', 'Enable/disable the remaining lessons update background service', 'bool', 1);
    
    PRINT 'RemainingLessonsUpdateEnabled configuration added successfully.';
END
ELSE
BEGIN
    PRINT 'RemainingLessonsUpdateEnabled configuration already exists.';
END

-- Check if RemainingLessonsUpdateIntervalMinutes config exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'RemainingLessonsUpdateIntervalMinutes')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [Visible])
    VALUES (200, 'RemainingLessonsUpdateIntervalMinutes', '60', 'Interval in minutes for updating remaining lessons count', 'int', 1);
    
    PRINT 'RemainingLessonsUpdateIntervalMinutes configuration added successfully.';
END
ELSE
BEGIN
    PRINT 'RemainingLessonsUpdateIntervalMinutes configuration already exists.';
END

GO
