-- ============================================================================
-- STEP-BY-STEP MIGRATION SCRIPT FOR ACCESSLEVEL
-- ============================================================================
-- Run each section separately to avoid parsing issues
-- Copy and paste each section one at a time into your SQL tool

-- ============================================================================
-- STEP 1: ADD ACCESSLEVEL COLUMN
-- ============================================================================
-- Copy and run this first:

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND name = 'AccessLevel')
BEGIN
    ALTER TABLE [dbo].[UserRoles]
    ADD [AccessLevel] [int] NOT NULL DEFAULT 10
    PRINT 'AccessLevel column added successfully'
END
ELSE
BEGIN
    PRINT 'AccessLevel column already exists'
END

-- ============================================================================
-- STEP 2: UPDATE ADMINISTRATOR ROLE
-- ============================================================================
-- After Step 1 succeeds, copy and run this:

UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 100 
WHERE [ID] = 1
PRINT 'Administrator role updated to AccessLevel 100'

-- ============================================================================
-- STEP 3: UPDATE TUTOR ROLE  
-- ============================================================================
-- Copy and run this:

UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 20 
WHERE [ID] = 2
PRINT 'Tutor role updated to AccessLevel 20'

-- ============================================================================
-- STEP 4: UPDATE STUDENT ROLE
-- ============================================================================
-- Copy and run this:

UPDATE [dbo].[UserRoles] 
SET [AccessLevel] = 10 
WHERE [ID] = 3
PRINT 'Student role updated to AccessLevel 10'

-- ============================================================================
-- STEP 5: ADD MANAGER ROLE
-- ============================================================================
-- Copy and run this:

IF NOT EXISTS (SELECT 1 FROM [dbo].[UserRoles] WHERE [ID] = 4)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([ID], [Description], [AccessLevel])
    VALUES (4, 'Manager', 80)
    PRINT 'Manager role created with AccessLevel 80'
END
ELSE
BEGIN
    UPDATE [dbo].[UserRoles] 
    SET [AccessLevel] = 80 
    WHERE [ID] = 4
    PRINT 'Existing Manager role updated to AccessLevel 80'
END

-- ============================================================================
-- STEP 6: ADD CONSTRAINT
-- ============================================================================
-- Copy and run this:

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserRoles_AccessLevel_Range')
BEGIN
    ALTER TABLE [dbo].[UserRoles]
    ADD CONSTRAINT CK_UserRoles_AccessLevel_Range 
    CHECK ([AccessLevel] >= 1 AND [AccessLevel] <= 100)
    PRINT 'AccessLevel range constraint added (1-100)'
END
ELSE
BEGIN
    PRINT 'AccessLevel range constraint already exists'
END

-- ============================================================================
-- STEP 7: VERIFY RESULTS
-- ============================================================================
-- Copy and run this to verify everything worked:

SELECT 
    [ID], 
    [Description], 
    [AccessLevel]
FROM [dbo].[UserRoles] 
ORDER BY [AccessLevel] DESC, [ID]

-- You should see:
-- ID=1, Description=Administrator, AccessLevel=100
-- ID=4, Description=Manager, AccessLevel=80  
-- ID=2, Description=Tutor, AccessLevel=20
-- ID=3, Description=Student, AccessLevel=10
