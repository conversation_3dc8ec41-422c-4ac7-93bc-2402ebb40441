USE [MusicSchool]
GO

-- Create EmailQueueAttachments table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailQueueAttachments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[EmailQueueAttachments](
        [ID] [int] IDENTITY(1,1) NOT NULL,
        [EmailQueueId] [int] NOT NULL,
        [AttachmentName] [nvarchar](256) NOT NULL,
        [AttachmentPath] [nvarchar](512) NOT NULL,
        [ContentType] [nvarchar](100) NULL,
        [FileSizeBytes] [bigint] NULL,
        [CreatedUTC] [datetime] NOT NULL DEFAULT GETUTCDATE(),
     CONSTRAINT [PK_EmailQueueAttachments] PRIMARY KEY CLUSTERED 
    (
        [ID] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]

    -- Add foreign key constraint
    ALTER TABLE [dbo].[EmailQueueAttachments]  WITH CHECK ADD  CONSTRAINT [FK_EmailQueueAttachments_EmailQueue] FOREIGN KEY([EmailQueueId])
    REFERENCES [dbo].[EmailQueue] ([ID])
    ON DELETE CASCADE;

    ALTER TABLE [dbo].[EmailQueueAttachments] CHECK CONSTRAINT [FK_EmailQueueAttachments_EmailQueue];

    -- Add index for performance
    CREATE NONCLUSTERED INDEX [IX_EmailQueueAttachments_EmailQueueId] ON [dbo].[EmailQueueAttachments]
    (
        [EmailQueueId] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY];

    PRINT 'EmailQueueAttachments table created successfully with foreign key and index.';
END
ELSE
BEGIN
    PRINT 'EmailQueueAttachments table already exists.';
END
