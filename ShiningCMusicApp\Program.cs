using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicApp.Services.Implementations;
using Syncfusion.Blazor;
using System.Net.Http.Json;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services.Email;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Load configuration with simplified logic
var appConfig = await LoadConfigurationAsync(builder);

// Register Syncfusion license
if (!string.IsNullOrEmpty(appConfig.SyncfusionLicense))
{
    Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(appConfig.SyncfusionLicense);
}

Console.WriteLine($"Configuration loaded - API: {appConfig.BffBaseUrl}, Session timeout: {appConfig.SessionTimeoutMinutes} minutes");

builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient (cookies are automatically handled by the browser in Blazor WebAssembly)
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Register configuration services
builder.Services.AddSingleton(appConfig);
builder.Services.AddScoped<BffConfiguration>(_ => new BffConfiguration { BaseUrl = appConfig.BffBaseUrl });

// Add authentication services
builder.Services.AddAuthorizationCore(options =>
{
    // Add access level policies
    for (int level = 10; level <= 100; level += 10)
    {
        options.AddPolicy($"AccessLevel{level}", policy =>
            policy.Requirements.Add(new ShiningCMusicApp.Authorization.AccessLevelRequirement(level)));
    }
});
builder.Services.AddScoped<Microsoft.AspNetCore.Authorization.IAuthorizationHandler, ShiningCMusicApp.Authorization.AccessLevelHandler>();
builder.Services.AddScoped<BffAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<BffAuthenticationStateProvider>());

// Add session timeout service with configured timeout minutes
builder.Services.AddScoped<ISessionTimeoutService>(provider =>
    new SessionTimeoutService(
        provider.GetRequiredService<IJSRuntime>(),
        provider.GetRequiredService<AuthenticationStateProvider>(),
        provider.GetRequiredService<NavigationManager>(),
        appConfig.SessionTimeoutMinutes));

// Add our services
builder.Services.AddScoped<IDialogService, DialogService>();

// Email services
builder.Services.AddScoped<IBulkEmailOrchestrator, BulkEmailOrchestrator>();
builder.Services.AddScoped<ISidebarThemeService, SidebarThemeService>();

// Legacy API services removed - all functionality moved to BFF services

// Add Web BFF services (new secure approach)
builder.Services.AddScoped<IWebBffAuthenticationService, BffAuthenticationService>();
builder.Services.AddScoped<IBffDashboardService, BffDashboardService>();
builder.Services.AddScoped<IBffTimesheetService, BffTimesheetService>();
builder.Services.AddScoped<IBffEmailTemplateService, BffEmailTemplateService>();
builder.Services.AddScoped<IBffEmailService, BffEmailService>();
builder.Services.AddScoped<IBffAdminService, BffAdminService>();
builder.Services.AddScoped<IBffSettingsService, BffSettingsService>();

// Add individual BFF services (domain-specific)
builder.Services.AddScoped<IBffLessonsService, BffLessonsService>();
builder.Services.AddScoped<IBffStudentsService, BffStudentsService>();
builder.Services.AddScoped<IBffTutorsService, BffTutorsService>();
builder.Services.AddScoped<IBffSubjectsService, BffSubjectsService>();
builder.Services.AddScoped<IBffLocationsService, BffLocationsService>();
builder.Services.AddScoped<IBffUsersService, BffUsersService>();
builder.Services.AddScoped<IBffConfigService, BffConfigService>();
builder.Services.AddScoped<IBffConfigurationService, BffConfigurationService>();
builder.Services.AddScoped<IBffMaintenanceService, BffMaintenanceService>();
builder.Services.AddScoped<IBffClientsService, BffClientsService>();

// Add Syncfusion Blazor service
builder.Services.AddSyncfusionBlazor();

await builder.Build().RunAsync();

// Configuration loading helper method
static async Task<AppConfiguration> LoadConfigurationAsync(WebAssemblyHostBuilder builder)
{
    // Load local configuration once
    var localHttp = new HttpClient() { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    Dictionary<string, string>? localConfig = null;

    try
    {
        localConfig = await localHttp.GetFromJsonAsync<Dictionary<string, string>>("appsettings.json");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to load local configuration: {ex.Message}");
    }

    // Determine BFF base URL
    var bffBaseUrl = DetermineBffBaseUrl(builder, localConfig);

    // Try to load server configuration
    var serverConfig = await TryLoadServerConfigurationAsync(bffBaseUrl);

    // Build final configuration with fallback chain: Server → Local → Defaults
    return BuildFinalConfiguration(serverConfig, localConfig, bffBaseUrl);
}

static string DetermineBffBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    if (builder.HostEnvironment.BaseAddress.Contains("azurewebsites.net"))
    {
        // Azure deployment - use same domain for now (will be separate later)
        var baseUri = new Uri(builder.HostEnvironment.BaseAddress);
        return $"{baseUri.Scheme}://{baseUri.Host}/bff";
    }

    // Local development - use local config or default (separate BFF service)
    return localConfig?.GetValueOrDefault("BffBaseUrl") ?? "https://localhost:7129/bff";
}

static async Task<AppConfiguration?> TryLoadServerConfigurationAsync(string bffBaseUrl)
{
    try
    {
        var configurationEndpoint = $"{bffBaseUrl}/configuration";
        Console.WriteLine($"Attempting to load configuration from: {configurationEndpoint}");

        using var http = new HttpClient();
        var serverConfig = await http.GetFromJsonAsync<AppConfiguration>(configurationEndpoint);

        if (serverConfig != null && !string.IsNullOrEmpty(serverConfig.BffBaseUrl))
        {
            Console.WriteLine("Successfully loaded server configuration");
            return serverConfig;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to load server configuration: {ex.Message}");
    }

    return null;
}

static AppConfiguration BuildFinalConfiguration(AppConfiguration? serverConfig, Dictionary<string, string>? localConfig, string fallbackBffBaseUrl)
{
    // Use server config if available, but ensure BFF URL is set
    if (serverConfig != null)
    {
        // If server config doesn't have BFF URL, use fallback
        if (string.IsNullOrEmpty(serverConfig.BffBaseUrl))
        {
            serverConfig.BffBaseUrl = fallbackBffBaseUrl;
        }
        return serverConfig;
    }

    // Fallback to local config with defaults
    var config = new AppConfiguration
    {
        BffBaseUrl = fallbackBffBaseUrl,
        SyncfusionLicense = localConfig?.GetValueOrDefault("SyncfusionLicense") ?? string.Empty,
        SessionTimeoutMinutes = 30, // Default
        ShowActionButtonLabel = false // Default
    };

    // Parse session timeout from local config
    if (localConfig?.TryGetValue("SessionTimeoutMinutes", out var timeoutStr) == true)
    {
        if (int.TryParse(timeoutStr, out var timeout))
        {
            config.SessionTimeoutMinutes = timeout;
        }
    }

    // Parse show action button label from local config
    if (localConfig?.TryGetValue("ShowActionButtonLabel", out var showLabelStr) == true)
    {
        if (bool.TryParse(showLabelStr, out var showLabel))
        {
            config.ShowActionButtonLabel = showLabel;
        }
    }

    Console.WriteLine("Using local configuration with defaults");
    return config;
}
