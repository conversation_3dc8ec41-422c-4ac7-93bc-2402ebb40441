@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using ShiningCMusicApp.Components.Buttons
@using ShiningCMusicCommon.Models

<!-- Template Selection Modal -->
<SfDialog @bind-Visible="IsVisible" Header="@Title" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <div class="mb-3">
                <label class="form-label">Select Email Template <span class="text-danger">*</span></label>
                <SfDropDownList TValue="string" TItem="string" @bind-Value="SelectedTemplateName"
                                DataSource="@GetTemplateNames()" CssClass="form-control" Placeholder="Choose a template">
                </SfDropDownList>
                @if (!string.IsNullOrWhiteSpace(SelectedTemplateName))
                {
                    var selectedTemplate = EmailTemplates.FirstOrDefault(t => t.Name == SelectedTemplateName);
                    if (selectedTemplate != null && !string.IsNullOrWhiteSpace(selectedTemplate.Subject))
                    {
                        <small class="text-muted mt-1 d-block">Subject: @selectedTemplate.Subject</small>
                    }
                }
            </div>
            <ModalActionButtonGroup FirstText="Send Email"
                                    FirstButtonClass="btn-success"
                                    FirstIcon="bi bi-envelope"
                                    OnFirstClick="OnSendTemplate"
                                    IsLoading="@IsLoading"
                                    IsFirstDisabled="@(IsLoading || string.IsNullOrEmpty(SelectedTemplateName))"
                                    SecondText="Cancel"
                                    OnSecondClick="HandleCancel" />
        </Content>
    </DialogTemplates>
</SfDialog>
