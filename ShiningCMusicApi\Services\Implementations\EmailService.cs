using System.Net;
using System.Net.Mail;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        private readonly IEmailTemplateService _emailTemplateService;
        private readonly IEmailQueueService _emailQueueService;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger,
            IEmailTemplateService emailTemplateService, IEmailQueueService emailQueueService)
        {
            _configuration = configuration;
            _logger = logger;
            _emailTemplateService = emailTemplateService;
            _emailQueueService = emailQueueService;
        }

        public async Task<bool> SendScheduleReadyEmailAsync(string tutorEmail, string tutorName)
        {
            if (string.IsNullOrWhiteSpace(tutorEmail))
            {
                _logger.LogWarning("Cannot send email: tutor email is empty");
                return false;
            }

            var placeholders = new Dictionary<string, string>
            {
                { "TutorName", tutorName }
            };

            return await SendEmailFromTemplateAsync("ScheduleReady", tutorEmail, placeholders);
        }

        public async Task<bool> SendPaymentReminderEmailAsync(string studentEmail, string studentName, int lessonsRemaining, string paymentDeadline)
        {
            if (string.IsNullOrWhiteSpace(studentEmail))
            {
                _logger.LogWarning("Cannot send payment reminder email: student email is empty");
                return false;
            }

            var placeholders = new Dictionary<string, string>
            {
                { "StudentName", studentName },
                { "LessonsRemaining", lessonsRemaining.ToString() },
                { "PaymentDeadline", paymentDeadline }
            };

            return await SendEmailFromTemplateAsync("PaymentReminder", studentEmail, placeholders);
        }

        public async Task<bool> SendEmailFromTemplateAsync(string templateName, string toEmail, Dictionary<string, string>? placeholders = null)
        {
            try
            {
                var template = await _emailTemplateService.GetTemplateAsync(templateName);
                if (template == null)
                {
                    _logger.LogError("Email template '{TemplateName}' not found", templateName);
                    return false;
                }

                var subject = ReplacePlaceholders(template.Subject ?? "", placeholders);
                var bodyHtml = !string.IsNullOrWhiteSpace(template.BodyHtml)
                    ? ReplacePlaceholders(template.BodyHtml, placeholders)
                    : null;
                var bodyText = !string.IsNullOrWhiteSpace(template.BodyText)
                    ? ReplacePlaceholders(template.BodyText, placeholders)
                    : null;

                // Queue the email instead of sending directly
                var emailId = await _emailQueueService.QueueEmailAsync(
                    toEmail: toEmail,
                    subject: subject,
                    body: bodyHtml ?? bodyText ?? "",
                    isHtml: !string.IsNullOrWhiteSpace(bodyHtml),
                    cc: template.CcEmailAddresses,
                    bcc: template.BccEmailAddresses
                );

                _logger.LogInformation("Email queued successfully with ID {EmailId} from template '{TemplateName}' to {Email}",
                    emailId, templateName, toEmail);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to queue email from template '{TemplateName}' to {Email}", templateName, toEmail);
                return false;
            }
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                // Queue the email instead of sending directly
                var emailId = await _emailQueueService.QueueEmailAsync(
                    toEmail: toEmail,
                    subject: subject,
                    body: body,
                    isHtml: isHtml
                );

                _logger.LogInformation("Email queued successfully with ID {EmailId} to {Email}", emailId, toEmail);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to queue email to {Email}", toEmail);
                return false;
            }
        }

        /// <summary>
        /// Send email directly via SMTP (used by background service)
        /// </summary>
        public async Task<bool> SendEmailDirectlyAsync(EmailQueue emailItem)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailItem.ToEmail))
                {
                    _logger.LogError("Cannot send email: ToEmail is empty for email ID {EmailId}", emailItem.ID);
                    return false;
                }

                // Read from environment variables first, then fall back to configuration
                var smtpServer = Environment.GetEnvironmentVariable("EMAIL_SMTP_SERVER")
                    ?? _configuration["EmailSettings:SmtpServer"];
                var smtpPortStr = Environment.GetEnvironmentVariable("EMAIL_SMTP_PORT")
                    ?? _configuration["EmailSettings:SmtpPort"] ?? "587";
                var smtpPort = int.Parse(smtpPortStr);
                var senderEmail = Environment.GetEnvironmentVariable("EMAIL_SENDER_EMAIL")
                    ?? _configuration["EmailSettings:SenderEmail"];
                var senderPassword = Environment.GetEnvironmentVariable("EMAIL_SENDER_PASSWORD")
                    ?? _configuration["EmailSettings:SenderPassword"];
                var senderName = Environment.GetEnvironmentVariable("EMAIL_SENDER_NAME")
                    ?? _configuration["EmailSettings:SenderName"];

                if (string.IsNullOrWhiteSpace(smtpServer) || string.IsNullOrWhiteSpace(senderEmail) || string.IsNullOrWhiteSpace(senderPassword))
                {
                    _logger.LogError("Email configuration is incomplete");
                    return false;
                }

                using var client = new SmtpClient(smtpServer, smtpPort)
                {
                    Credentials = new NetworkCredential(senderEmail, senderPassword),
                    EnableSsl = true
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(senderEmail, senderName),
                    Subject = emailItem.Subject ?? "",
                    Body = emailItem.Body,
                    IsBodyHtml = emailItem.IsHtml
                };

                mailMessage.To.Add(emailItem.ToEmail);

                // Add CC recipients
                if (!string.IsNullOrWhiteSpace(emailItem.CC))
                {
                    var ccAddresses = emailItem.CC.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var cc in ccAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(cc.Trim()))
                        {
                            mailMessage.CC.Add(cc.Trim());
                        }
                    }
                }

                // Add BCC recipients
                if (!string.IsNullOrWhiteSpace(emailItem.BCC))
                {
                    var bccAddresses = emailItem.BCC.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var bcc in bccAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(bcc.Trim()))
                        {
                            mailMessage.Bcc.Add(bcc.Trim());
                        }
                    }
                }

                // Add attachments from EmailQueue
                if (emailItem.Attachments != null && emailItem.Attachments.Any())
                {
                    foreach (var attachment in emailItem.Attachments)
                    {
                        if (File.Exists(attachment.AttachmentPath))
                        {
                            var mailAttachment = new Attachment(attachment.AttachmentPath)
                            {
                                Name = attachment.AttachmentName
                            };

                            if (!string.IsNullOrWhiteSpace(attachment.ContentType))
                            {
                                mailAttachment.ContentType = new System.Net.Mime.ContentType(attachment.ContentType);
                            }

                            mailMessage.Attachments.Add(mailAttachment);
                        }
                        else
                        {
                            _logger.LogWarning("Attachment file not found: {AttachmentPath} for email ID {EmailId}",
                                attachment.AttachmentPath, emailItem.ID);
                        }
                    }
                }

                await client.SendMailAsync(mailMessage);
                _logger.LogInformation("Email sent successfully to {Email} for queue ID {EmailId} with {AttachmentCount} attachments",
                    emailItem.ToEmail, emailItem.ID, emailItem.Attachments?.Count ?? 0);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email} for queue ID {EmailId}", emailItem.ToEmail, emailItem.ID);
                return false;
            }
        }

        private static string ReplacePlaceholders(string content, Dictionary<string, string>? placeholders)
        {
            if (string.IsNullOrWhiteSpace(content) || placeholders == null || placeholders.Count == 0)
            {
                return content;
            }

            var result = content;
            foreach (var placeholder in placeholders)
            {
                result = result.Replace($"{{{placeholder.Key}}}", placeholder.Value);
            }

            return result;
        }
    }
}
