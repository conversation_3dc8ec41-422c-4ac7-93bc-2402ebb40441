# ModalActionButtonGroup Component

A reusable Blazor component for creating consistent action button groups in modals and forms across the application.

## Features

- **Consistent Styling**: Uses your existing CSS classes (`btn-blue-custom`, `btn-cancel-custom`, etc.)
- **Loading State**: Built-in spinner support for async operations
- **Flexible Configuration**: Support for 1-3 buttons with customizable icons, text, and styling
- **Bootstrap Integration**: Uses Bootstrap classes for responsive layout
- **Syncfusion Integration**: Built on SfButton components

## Basic Usage

### Two-Button Layout (Save/Cancel)
```razor
<ModalActionButtonGroup
    FirstText="Create"
    FirstIcon="bi bi-plus-circle"
    OnFirstClick="CreateBulkTimesheetEntries"
    IsLoading="@isSavingEntry"
    IsFirstDisabled="@isSavingEntry"
    SecondText="Cancel"
    OnSecondClick="CloseBulkEntryModal" />
```

### Single Button
```razor
<ModalActionButtonGroup
    FirstText="Close"
    FirstIcon="bi bi-x-circle"
    OnFirstClick="CloseModal"
    ShowSecondButton="false" />
```

### Three-Button Layout
```razor
<ModalActionButtonGroup
    FirstText="Save"
    OnFirstClick="SaveData"
    SecondText="Cancel"
    OnSecondClick="Cancel"
    ShowThirdButton="true"
    ThirdText="Delete"
    ThirdIcon="bi bi-trash"
    ThirdButtonClass="btn-danger"
    OnThirdClick="DeleteData" />
```

## Parameters

### First Button
- `ShowFirstButton` (bool): Show/hide first button (default: true)
- `FirstText` (string): Button text (default: "Save")
- `FirstIcon` (string): Bootstrap icon class (default: "bi bi-check-circle")
- `FirstIconColor` (string): Icon color (default: "white")
- `FirstButtonClass` (string): CSS class (default: "btn-blue-custom")
- `FirstButtonType` (string): Button type (default: "button")
- `IsFirstDisabled` (bool): Disable button (default: false)
- `OnFirstClick` (EventCallback): Click handler

### Second Button
- `ShowSecondButton` (bool): Show/hide second button (default: true)
- `SecondText` (string): Button text (default: "Cancel")
- `SecondIcon` (string): Bootstrap icon class (default: "bi bi-x-circle")
- `SecondIconColor` (string): Icon color (default: "inherit")
- `SecondButtonClass` (string): CSS class (default: "btn-cancel-custom")
- `SecondButtonType` (string): Button type (default: "button")
- `IsSecondDisabled` (bool): Disable button (default: false)
- `OnSecondClick` (EventCallback): Click handler

### Third Button
- `ShowThirdButton` (bool): Show/hide third button (default: false)
- `ThirdText` (string): Button text (default: "")
- `ThirdIcon` (string): Bootstrap icon class (default: "")
- `ThirdIconColor` (string): Icon color (default: "white")
- `ThirdButtonClass` (string): CSS class (default: "btn-success")
- `ThirdButtonType` (string): Button type (default: "button")
- `IsThirdDisabled` (bool): Disable button (default: false)
- `OnThirdClick` (EventCallback): Click handler

### General
- `IsLoading` (bool): Show spinner on first button (default: false)
- `ShowContainer` (bool): Whether to wrap buttons in container div (default: true)
- `ContainerClass` (string): Container CSS classes (default: "d-flex justify-content-end gap-2 mt-4")

## Common Patterns

### Form Submit Buttons
```razor
<ModalActionButtonGroup
    FirstText="Save"
    FirstButtonType="submit"
    OnFirstClick="HandleSubmit"
    IsLoading="@isSaving"
    IsFirstDisabled="@isSaving"
    SecondText="Cancel"
    OnSecondClick="CloseModal" />
```

### Modal Footer Buttons
```razor
<ModalActionButtonGroup
    FirstText="Confirm"
    FirstIcon="bi bi-check-circle"
    OnFirstClick="ConfirmAction"
    SecondText="Cancel"
    OnSecondClick="CloseModal" />
```

### Custom Styling
```razor
<ModalActionButtonGroup
    FirstText="Delete"
    FirstIcon="bi bi-trash"
    FirstButtonClass="btn-danger"
    OnFirstClick="DeleteItem"
    SecondText="Cancel"
    OnSecondClick="Cancel"
    ContainerClass="d-flex justify-content-center gap-3 mt-3" />
```

### Buttons Without Container
Use `ShowContainer="false"` when you want to place buttons in your own custom container:

```razor
<div class="my-custom-container d-flex justify-content-between">
    <span>Some other content</span>
    <div class="d-flex gap-2">
        <ModalActionButtonGroup
            FirstText="Save"
            SecondText="Cancel"
            OnFirstClick="Save"
            OnSecondClick="Cancel"
            ShowContainer="false" />
    </div>
</div>
```

This renders just the buttons without the default container div, giving you full control over the layout.

## Migration from Existing Code

### Before
```razor
<div class="d-flex justify-content-end gap-2 mt-4">
    <SfButton CssClass="btn btn-blue-custom" @onclick="CreateBulkTimesheetEntries" Disabled="@isSavingEntry">
        @if (isSavingEntry)
        {
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
        }
        else
        {
            <i class="bi bi-plus-circle" style="color: white;"></i><span class="ms-2">Create</span>
        }
    </SfButton>
    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseBulkEntryModal">
        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
    </SfButton>
</div>
```

### After
```razor
<ModalActionButtonGroup
    FirstText="Create"
    FirstIcon="bi bi-plus-circle"
    OnFirstClick="CreateBulkTimesheetEntries"
    IsLoading="@isSavingEntry"
    IsFirstDisabled="@isSavingEntry"
    SecondText="Cancel"
    OnSecondClick="CloseBulkEntryModal" />
```

## Available CSS Classes

The component supports all your existing button classes:
- `btn-blue-custom` (default primary)
- `btn-cancel-custom` (default secondary)
- `btn-danger`
- `btn-success`
- `btn-warning`

## Notes

- The component automatically handles icon spacing and loading states
- Icons are optional - if not provided, only text will be shown
- The loading spinner automatically replaces the icon when `IsLoading` is true
- All buttons support the standard Bootstrap icon classes (`bi bi-*`)
