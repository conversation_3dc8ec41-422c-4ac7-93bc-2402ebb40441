using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffAdminService
    {
        Task<AdminData?> GetAdminDataAsync();
        
        // User Management
        Task<bool> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(string loginName);
        
        // Subject Management
        Task<bool> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(Subject subject);
        Task<bool> DeleteSubjectAsync(int subjectId);
        
        // Location Management
        Task<bool> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(Location location);
        Task<bool> DeleteLocationAsync(int locationId);

        // User Role Management
        Task<bool> CreateUserRoleAsync(UserRole userRole);
        Task<bool> UpdateUserRoleAsync(UserRole userRole);
        Task<bool> DeleteUserRoleAsync(int roleId);
    }
}
