using ShiningCMusicDbManager.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ShiningCMusicDbManager.Services
{
    /// <summary>
    /// Service for managing database migrations
    /// </summary>
    public interface IDatabaseMigrationService
    {
        /// <summary>
        /// Applies all pending migrations to the database
        /// </summary>
        /// <returns>Result of the migration operation</returns>
        Task<MigrationResult> ApplyAllMigrationsAsync();

        /// <summary>
        /// Applies a specific migration script to the database
        /// </summary>
        /// <param name="scriptName">Name of the script to apply</param>
        /// <returns>Result of the migration operation</returns>
        Task<MigrationResult> ApplySpecificMigrationAsync(string scriptName);

        /// <summary>
        /// Gets the status of all migration scripts
        /// </summary>
        /// <returns>List of migration statuses</returns>
        Task<List<MigrationStatus>> GetMigrationStatusAsync();

        /// <summary>
        /// Validates that the database connection is working
        /// </summary>
        /// <returns>True if connection is valid, false otherwise</returns>
        Task<bool> ValidateDatabaseConnectionAsync();

        /// <summary>
        /// Rolls back a specific migration (if rollback script exists)
        /// </summary>
        /// <param name="scriptName">Name of the script to rollback</param>
        /// <returns>Result of the rollback operation</returns>
        Task<MigrationResult> RollbackMigrationAsync(string scriptName);

        /// <summary>
        /// Forces a re-application of a specific migration script
        /// </summary>
        /// <param name="scriptName">Name of the script to force apply</param>
        /// <returns>Result of the migration operation</returns>
        Task<MigrationResult> ForceApplyMigrationAsync(string scriptName);

        /// <summary>
        /// Gets detailed information about a specific migration script
        /// </summary>
        /// <param name="scriptName">Name of the script</param>
        /// <returns>Migration status with detailed information</returns>
        Task<MigrationStatus?> GetMigrationDetailsAsync(string scriptName);

        /// <summary>
        /// Discovers all SQL scripts in the configured directory
        /// </summary>
        /// <returns>List of discovered SQL scripts</returns>
        Task<List<SqlScript>> DiscoverScriptsAsync();

        /// <summary>
        /// Validates that all script dependencies are satisfied
        /// </summary>
        /// <returns>List of dependency validation issues</returns>
        Task<List<string>> ValidateDependenciesAsync();

        /// <summary>
        /// Creates a backup of the database before applying migrations
        /// </summary>
        /// <returns>Path to the backup file if successful</returns>
        Task<string?> CreateDatabaseBackupAsync();

        /// <summary>
        /// Marks a migration as applied without actually executing it
        /// </summary>
        /// <param name="scriptName">Name of the script to mark as applied</param>
        /// <param name="appliedBy">User or system marking the migration</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkMigrationAsAppliedAsync(string scriptName, string appliedBy);

        /// <summary>
        /// Removes a migration record from the tracking table
        /// </summary>
        /// <param name="scriptName">Name of the script to remove</param>
        /// <returns>True if successful</returns>
        Task<bool> RemoveMigrationRecordAsync(string scriptName);

        /// <summary>
        /// Initializes the migration tracking system
        /// </summary>
        /// <returns>True if successful</returns>
        Task<bool> InitializeMigrationSystemAsync();
    }
}
