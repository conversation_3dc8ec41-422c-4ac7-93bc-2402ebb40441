using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class PaymentReminderService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PaymentReminderService> _logger;
        private readonly IConfiguration _configuration;
        private TimeSpan _period;
        private int _lessonThreshold;
        private int _paymentDeadlineDays;
        private bool _isEnabled;

        public PaymentReminderService(
            IServiceProvider serviceProvider,
            ILogger<PaymentReminderService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Initialize with default values - will be updated from database during execution
            _period = TimeSpan.FromHours(24);
            _lessonThreshold = 3;
            _paymentDeadlineDays = 7;
            _isEnabled = true;

            _logger.LogInformation("PaymentReminderService initialized.");
        }

        private async Task<bool> LoadConfigurationAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var configService = scope.ServiceProvider.GetRequiredService<IConfigService>();

                // Check if service is enabled
                _isEnabled = await configService.IsBackgroundProcessorEnabledAsync("PaymentReminder");
                if (!_isEnabled)
                {
                    _logger.LogInformation("PaymentReminderService is disabled in configuration.");
                    return false;
                }

                // Load configuration values with fallback to environment variables and appsettings.json
                var intervalHours = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "PaymentReminderIntervalHours",
                    "PAYMENT_REMINDER_INTERVAL_HOURS", "PaymentReminder:IntervalHours", 24);
                _lessonThreshold = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "PaymentReminderLessonThreshold",
                    "PAYMENT_REMINDER_LESSON_THRESHOLD", "PaymentReminder:LessonThreshold", 3);
                _paymentDeadlineDays = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "PaymentReminderDeadlineDays",
                    "PAYMENT_REMINDER_DEADLINE_DAYS", "PaymentReminder:PaymentDeadlineDays", 7);

                _period = TimeSpan.FromHours(intervalHours);

                _logger.LogInformation("PaymentReminderService configuration loaded: Interval={Hours}h, LessonThreshold={Threshold}, DeadlineDays={Days}, Enabled={Enabled}",
                    intervalHours, _lessonThreshold, _paymentDeadlineDays, _isEnabled);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load configuration from database, using fallback values.");

                // Fallback to original configuration method
                var intervalHours = GetConfigurationValue("PAYMENT_REMINDER_INTERVAL_HOURS", "PaymentReminder:IntervalHours", 24);
                _lessonThreshold = GetConfigurationValue("PAYMENT_REMINDER_LESSON_THRESHOLD", "PaymentReminder:LessonThreshold", 3);
                _paymentDeadlineDays = GetConfigurationValue("PAYMENT_REMINDER_DEADLINE_DAYS", "PaymentReminder:PaymentDeadlineDays", 7);
                _period = TimeSpan.FromHours(intervalHours);
                _isEnabled = true;
                return true;
            }
        }

        private async Task<int> GetConfigValueWithFallbackAsync(IConfigService configService, int groupId, string key,
            string envVar, string configKey, int defaultValue)
        {
            // Try database first
            var dbValue = await configService.GetConfigValueAsync<int?>(groupId, key);
            if (dbValue.HasValue)
            {
                _logger.LogInformation("Using database config {GroupId}:{Key} = {Value}", groupId, key, dbValue.Value);
                return dbValue.Value;
            }

            // Fall back to environment variable
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", envVar, envIntValue);
                return envIntValue;
            }

            // Fall back to appsettings.json
            var configValue = _configuration.GetValue<int>(configKey, defaultValue);
            _logger.LogInformation("Using appsettings.json {ConfigKey} = {Value} (default: {Default})",
                configKey, configValue, defaultValue);
            return configValue;
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                return envIntValue;
            }

            // Then try configuration
            var configValue = _configuration[configurationKey];
            if (!string.IsNullOrEmpty(configValue) && int.TryParse(configValue, out var configIntValue))
            {
                return configIntValue;
            }

            // Return default value
            return defaultValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("PaymentReminderService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Load configuration from database before each execution
                    var configLoaded = await LoadConfigurationAsync();
                    if (configLoaded && _isEnabled)
                    {
                        await SendPaymentRemindersAsync();
                    }
                    else if (!_isEnabled)
                    {
                        _logger.LogDebug("PaymentReminderService is disabled, skipping reminder processing.");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during payment reminder processing.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("PaymentReminderService stopped.");
        }

        private async Task SendPaymentRemindersAsync()
        {
            _logger.LogInformation("Starting payment reminder process...");

            using var scope = _serviceProvider.CreateScope();
            var remainingLessonsService = scope.ServiceProvider.GetRequiredService<IRemainingLessonsService>();
            var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();

            try
            {
                var studentsWithLowLessons = await remainingLessonsService.GetStudentsWithRemainingLessonsAsync(_lessonThreshold);
                var remindersSent = 0;

                foreach (var student in studentsWithLowLessons)
                {
                    try
                    {
                        var paymentDeadline = DateTime.Now.AddDays(_paymentDeadlineDays).ToString("MMMM dd, yyyy");
                        
                        var placeholders = new Dictionary<string, string>
                        {
                            { "StudentName", student.StudentName },
                            { "LessonsRemaining", student.RemainingLessons.ToString() },
                            { "PaymentDeadline", paymentDeadline }
                        };

                        var success = await emailService.SendEmailFromTemplateAsync("PaymentReminder", student.Email, placeholders);
                        
                        if (success)
                        {
                            remindersSent++;
                            _logger.LogInformation("Payment reminder sent to student {StudentName} ({Email}) with {RemainingLessons} lessons remaining.",
                                student.StudentName, student.Email, student.RemainingLessons);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to send payment reminder to student {StudentName} ({Email}).",
                                student.StudentName, student.Email);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending payment reminder to student {StudentName} ({Email}).",
                            student.StudentName, student.Email);
                    }
                }

                if (remindersSent > 0)
                {
                    _logger.LogInformation("Successfully sent {Count} payment reminder(s).", remindersSent);
                }
                else
                {
                    _logger.LogInformation("No payment reminders needed at this time.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process payment reminders.");
                throw;
            }
        }
    }
}
