# Shining C Music School App

A comprehensive music school management application built with Blazor WebAssembly and ASP.NET Core.

## Features

### Core Functionality
- **User Management**: Students, tutors, and administrators
- **Lesson Scheduling**: Interactive calendar with Syncfusion Scheduler
- **Multi-Tutor Filtering**: Select multiple tutors to view combined schedules
- **Recurring Events**: Enterprise-grade recurrence editor with iCalendar compliance
- **Precise Time Selection**: 15-minute interval time pickers for accurate scheduling
- **Timesheet Management**: Comprehensive timesheet tracking with auto-entry creation
- **Role-Based Access**: Different permissions for students, tutors, and admins
- **Responsive Design**: Mobile-friendly interface with adaptive layouts

### Security Features
- **Access Level Based Permissions**: Granular permission control using numeric access levels
  - Level 10: Student access
  - Level 20: Tutor access
  - Level 80: Manager access
  - Level 100: Administrator access
- **Session Timeout**: Automatic logout after configurable period of inactivity (default: 30 minutes)
- **Password Encryption**: Secure password storage
- **Environment-Based Configuration**: Secure configuration management
- **B<PERSON> Pattern**: Secure Backend-For-Frontend implementation

### Session Timeout
The application includes an automatic session timeout feature for enhanced security:

- **Default Timeout**: 30 minutes of inactivity
- **Activity Detection**: Monitors user interactions (mouse, keyboard, scroll, touch)
- **Automatic Logout**: Redirects to login page when timeout expires
- **Configurable**: Set via environment variable or configuration file

#### Configuration
```bash
# Environment Variable (Recommended for Production)
SESSION_TIMEOUT_MINUTES=30
```

```json
// appsettings.json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

### Recurring Events & Scheduling
The application features a modernized recurring event system with enterprise-grade components:

- **Syncfusion SfRecurrenceEditor**: Professional recurrence pattern configuration
- **iCalendar Compliance**: RFC 5545 standard RRULE generation
- **15-Minute Intervals**: Precise time selection with SfDateTimePicker components
- **Smart UI**: Recurrence options hidden when editing single occurrences
- **Auto-Focus**: Smooth scrolling to recurrence section when selecting frequencies
- **Automatic Validation**: Built-in error handling and validation
- **Intelligent Next Lesson Display**: Shows actual next occurrence for recurring events

#### Supported Recurrence Patterns
- **Daily**: Every N days with configurable intervals
- **Weekly**: Specific days of the week with custom intervals
- **Monthly**: Monthly patterns with flexible end conditions
- **End Conditions**: Never, after X occurrences, or until specific date

### Multi-Tutor Filtering & Schedule Management
The application features an advanced tutor filtering system for efficient schedule management:

- **Clickable Tutor Names**: Tutor names in the color section are clickable for selection
- **Multi-Selection**: Select multiple tutors simultaneously to view combined schedules
- **Toggle Selection**: Click selected tutors to remove them from the filter
- **Visual Feedback**: Selected tutors highlighted with blue color and check icons
- **Selection Counter**: Shows count of selected tutors with easy clear option
- **Real-time Filtering**: Immediate schedule updates with lesson count display

#### How to Use Tutor Filtering
1. **Navigate** to the Lessons page as an administrator
2. **Expand** the Tutor Colors section
3. **Click** any tutor name to select/deselect them for filtering
4. **View** lessons for selected tutors only
5. **Clear** all selections with the "Show All Tutors" button

#### Benefits for Administrators
- **Schedule Comparison**: Compare multiple tutors' schedules simultaneously
- **Workload Management**: Quickly assess lesson distribution across tutors
- **Conflict Resolution**: Identify scheduling conflicts between specific tutors
- **Efficient Planning**: Focus on relevant tutors when making scheduling decisions

### Timesheet Management & Auto-Entry Creation
The application features comprehensive timesheet management with streamlined entry creation:

- **Auto-Entry Creation**: Create timesheet and attendance records in one operation
- **Flexible Entry Count**: Specify 0-50 attendance records to create automatically
- **Smart Defaults**: Default to 5 entries with option to create none (0)
- **Bulk Operations**: Create multiple attendance records with sequential numbering
- **Excel Export**: Export timesheets with attendance data to Excel format
- **Role-Based Access**: Administrators can create/edit, tutors have read-only access

#### How to Use Auto-Entry Creation
1. **Navigate** to the Timesheets page as an administrator
2. **Click** "Add New Timesheet" button
3. **Fill** in timesheet details (student, tutor, subject, etc.)
4. **Specify** number of attendance records to create (0-50, default: 5)
5. **Save** to create timesheet and attendance records simultaneously

#### Benefits of Auto-Entry Creation
- **Time Saving**: Create multiple attendance records in one step
- **Consistency**: Sequential record numbering and standardized format
- **Flexibility**: Choose exact number of records needed or create none
- **Error Prevention**: Robust handling ensures timesheet creation even if entries fail
- **User-Friendly**: Clear feedback on creation success and record counts

## Technology Stack

### Frontend
- **Blazor WebAssembly**: Client-side web application framework
- **Syncfusion Components**: Professional UI components for scheduling and data management
- **Bootstrap 5**: Responsive CSS framework
- **JavaScript Integration**: Custom activity tracking and mobile menu functionality

### Backend
- **ASP.NET Core Web API**: RESTful API services
- **Entity Framework Core**: Database access and ORM
- **Authentication & Authorization**:
  - **OpenIddict 7.0.0**: OAuth2/OpenID Connect server implementation
  - **JWT Tokens**: Secure API authentication
  - **Cookie Authentication**: Secure BFF session management
  - **Claims-based Authorization**: User claims and access levels
- **SQL Server**: Database storage

### Deployment
- **Azure App Service**: Cloud hosting platform
- **Environment Variables**: Configuration management
- **CORS**: Cross-origin resource sharing configuration

## Project Structure

```
ShiningCMusicApp/
├── ShiningCMusicApp/          # Blazor WebAssembly Client
│   ├── Components/            # Reusable Blazor components
│   ├── Pages/                 # Application pages
│   ├── Services/              # Client-side services
│   ├── Layout/                # Application layout components
│   └── wwwroot/               # Static files and assets
├── ShiningCMusicApi/          # ASP.NET Core Web API
│   ├── Controllers/           # API controllers
│   ├── Services/              # Business logic services
│   └── Infrastructure/        # Database and configuration
├── ShiningCMusicCommon/       # Shared models and utilities
└── Documentation/             # Project documentation
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or VS Code

### Configuration

#### Database Connection
Set the connection string in `appsettings.json` or environment variable:
```json
{
  "ConnectionStrings": {
    "MusicSchool": "Server=(localdb)\\mssqllocaldb;Database=MusicSchool;Trusted_Connection=true"
  }
}
```

#### Session Timeout (Optional)
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

### Running the Application

1. **Clone the repository**
2. **Restore packages**: `dotnet restore`
3. **Update database**: Run SQL scripts in `ShiningCMusicApi/SQL/`
4. **Start API**: `dotnet run --project ShiningCMusicApi`
5. **Start Client**: `dotnet run --project ShiningCMusicApp`

### Default Users
- **Admin**: admin/password
- **Tutor**: tutor/password  
- **Student**: student/password

## Documentation

### Feature Documentation
- [Access Level Permissions](../Documentation/AccessLevelPermissions.md) - Complete permission system documentation
- [BFF Architecture](../Documentation/BFF_Architecture.md) - Backend-For-Frontend implementation details
- [OpenIddict Implementation](../Documentation/OpenIddict_Implementation.md) - Authentication and authorization details
- [Multi-Tutor Filtering](Documentation/Multi_Tutor_Filtering.md) - Complete multi-tutor selection and filtering system
- [UI Improvements](Documentation/UI_Improvements.md) - Complete UI enhancements including multi-tutor filtering
- [Syncfusion Recurrence Editor Migration](Documentation/Syncfusion_Recurrence_Editor_Migration.md) - Modern recurrence UI implementation
- [Recurring Events Implementation](Documentation/Recurring_Events_Implementation.md) - Complete recurring events system
- [Session Timeout Implementation](Documentation/SessionTimeout_Implementation.md) - Complete session timeout documentation
- [Session Timeout Quick Reference](Documentation/SessionTimeout_QuickReference.md) - Quick setup guide
- [Mobile Improvements](Documentation/MOBILE_IMPROVEMENTS.md) - Mobile interface enhancements
- [Recurrence Focus Implementation](Documentation/Recurrence_Focus_Implementation.md) - Auto-scroll to recurrence options
- [Password Validation](Documentation/Client_Password_Validation.md) - Client-side validation

### API Documentation
- Swagger UI available at `/swagger` when running in development mode
- RESTful API endpoints for all major functionality
- Authentication via IdentityServer4 OAuth2/OpenID Connect

## Deployment

### Azure App Service
1. **Create App Service** in Azure Portal
2. **Set Environment Variables**:
   - `DATABASE_CONNECTION_STRING`: Database connection string
   - `SESSION_TIMEOUT_MINUTES`: Session timeout duration
   - `API_BASE_URL`: API base URL
3. **Deploy** via Visual Studio or Azure DevOps

### Environment Variables
- `SESSION_TIMEOUT_MINUTES`: Session timeout in minutes (default: 30)
- `API_BASE_URL`: Base URL for API endpoints
- `DATABASE_CONNECTION_STRING`: Database connection string
- `SYNCFUSION_LICENSE`: Syncfusion license key

## Security Considerations

- **Session Management**: Automatic timeout prevents unauthorized access
- **Password Security**: Encrypted password storage
- **Role-Based Access**: Granular permissions by user role
- **CORS Configuration**: Controlled cross-origin access
- **Environment Variables**: Sensitive configuration via environment variables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is proprietary software for Shining C Music School.

## Support

For technical support or questions, please contact the development team.
