namespace ShiningCMusicCommon.Models
{
    public class DashboardData
    {
        public List<ScheduleEvent> Lessons { get; set; } = new();
        public List<Student> Students { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
        public List<Location> Locations { get; set; } = new();
        public DashboardStats Stats { get; set; } = new();
        public int AccessLevel { get; set; }
        public DateTime LoadedAt { get; set; }
    }

    public class DashboardStats
    {
        public int TotalLessons { get; set; }
        public int TotalStudents { get; set; }
        public int TotalTutors { get; set; }
        public int TotalSubjects { get; set; }
        public int UpcomingLessons { get; set; }
        public int TodayLessons { get; set; }
        public int ThisWeekLessons { get; set; }
    }

    public class AdminData
    {
        public List<User> Users { get; set; } = new();
        public List<UserRole> UserRoles { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
        public List<Location> Locations { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Student> Students { get; set; } = new();
    }
}
