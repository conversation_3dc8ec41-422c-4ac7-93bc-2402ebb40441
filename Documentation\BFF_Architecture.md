# Backend-For-Frontend (BFF) Architecture

## Overview

The ShiningC Music App implements the Backend-For-Frontend (BFF) pattern to provide a secure and optimized interface between the Blazor WebAssembly frontend and the core API services. This pattern helps manage authentication, request aggregation, and data transformation specific to the web client needs.

## Architecture Components

### 1. Frontend (Blazor WebAssembly)
```plaintext
ShiningCMusicApp/
├── Pages/           # Blazor pages
├── Components/      # Reusable UI components
├── Services/        # BFF client services
│   ├── Interfaces/
│   │   ├── IWebBffAuthenticationService.cs
│   │   ├── IBffDashboardService.cs
│   │   └── ...
│   └── Implementations/
│       ├── BffAuthenticationService.cs
│       ├── BffDashboardService.cs
│       └── ...
└── Authorization/   # Authentication state handling
```

### 2. BFF Layer (ASP.NET Core)
```plaintext
ShiningCMusicBFF/
├── Controllers/     # BFF API endpoints
│   ├── AuthController.cs
│   ├── DashboardController.cs
│   └── ...
├── Services/        # Core API client services
└── Configuration/   # BFF-specific configuration
```

### 3. Core API (ASP.NET Core)
```plaintext
ShiningCMusicApi/
├── Controllers/     # Core business logic endpoints
├── Services/        # Business logic services
└── Data/           # Data access layer
```

## Authentication Flow

1. **Initial Authentication**
```mermaid
sequenceDiagram
    Browser->>BFF: POST /bff/auth/login
    BFF->>CoreAPI: Authenticate credentials
    CoreAPI-->>BFF: JWT + User info
    BFF->>BFF: Create session cookie
    BFF-->>Browser: Set secure HTTP-only cookie
```

2. **Subsequent Requests**
```mermaid
sequenceDiagram
    Browser->>BFF: Request with session cookie
    BFF->>BFF: Validate session
    BFF->>CoreAPI: Request with JWT
    CoreAPI-->>BFF: Response
    BFF-->>Browser: Processed response
```

## Implementation Details

### 1. Authentication State Provider

```csharp
public class BffAuthenticationStateProvider : AuthenticationStateProvider
{
    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        // Check authentication state
        var isAuthenticated = await _bffAuthService.IsAuthenticatedAsync();
        
        if (!isAuthenticated)
            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));

        // Get user info and create claims
        var user = await _bffAuthService.GetCurrentUserAsync();
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, user.UserName ?? user.LoginName),
            new Claim(ClaimTypes.NameIdentifier, user.LoginName),
            new Claim("UserId", user.LoginName),
            new Claim("RoleId", user.RoleId?.ToString() ?? "0"),
            new Claim("AccessLevel", user.RoleAccessLevel?.ToString() ?? "0")
        };

        return new AuthenticationState(new ClaimsPrincipal(
            new ClaimsIdentity(claims, "BffAuthentication")));
    }
}
```

### 2. BFF Controllers

BFF controllers aggregate and transform data for specific frontend needs:

```csharp
[ApiController]
[Route("bff/dashboard")]
[Authorize]
public class DashboardController : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<DashboardData>> GetDashboardData()
    {
        // Load all required data in parallel
        var lessonsTask = _apiClient.GetJsonAsync<List<ScheduleEvent>>("lessons");
        var studentsTask = _apiClient.GetJsonAsync<List<Student>>("students");
        var tutorsTask = _apiClient.GetJsonAsync<List<Tutor>>("tutors");
        // ...

        // Filter based on access level
        var accessLevel = User.GetAccessLevel();
        if (accessLevel < 80)
        {
            // Apply user-specific filters
        }

        // Return aggregated response
        return new DashboardData
        {
            Lessons = lessonsList,
            Students = studentsList,
            // ...
        };
    }
}
```

### 3. Frontend Services

BFF client services in the Blazor app:

```csharp
public class BffDashboardService : IBffDashboardService
{
    public async Task<DashboardData?> GetDashboardDataAsync()
    {
        var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/dashboard");
        request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

        var response = await _httpClient.SendAsync(request);
        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<DashboardData>();
        }
        return null;
    }
}
```

## Security Features

1. **Session Management**
   - HTTP-only cookies for session state
   - Automatic session timeout handling
   - CSRF protection

2. **Authentication**
   - Secure cookie-based authentication
   - No JWT exposure to client
   - Access level-based authorization

3. **Request Handling**
   - Input validation
   - Response transformation
   - Error handling and logging

## Data Aggregation

The BFF layer optimizes frontend performance by:

1. **Parallel Request Handling**
   ```csharp
   var tasksTask = _apiClient.GetJsonAsync<List<Task>>("tasks");
   var userTask = _apiClient.GetJsonAsync<UserProfile>("users/current");
   await Task.WhenAll(tasksTask, userTask);
   ```

2. **Response Shaping**
   - Filtering sensitive data
   - Combining multiple API responses
   - Transforming data formats

3. **Caching**
   - Response caching where appropriate
   - Cache invalidation handling

## Error Handling

1. **Global Exception Handling**
   ```csharp
   try
   {
       // API calls
   }
   catch (Exception ex)
   {
       _logger.LogError(ex, "Error in BFF operation");
       return StatusCode(500, new { message = "An error occurred" });
   }
   ```

2. **Error Transformation**
   - Converting API errors to frontend-friendly formats
   - Maintaining security by filtering sensitive error details

## Best Practices

1. **Controller Design**
   - Keep controllers focused on frontend needs
   - Implement proper error handling
   - Use async/await consistently
   - Document API endpoints

2. **Security**
   - Validate all inputs
   - Apply proper authorization
   - Use HTTPS
   - Implement proper CORS policies

3. **Performance**
   - Use parallel requests where possible
   - Implement caching strategically
   - Monitor performance metrics

4. **Maintenance**
   - Keep documentation updated
   - Monitor error logs
   - Regular security updates
   - Clean code practices

## Configuration

1. **appsettings.json**
```json
{
  "BffConfig": {
    "CoreApiUrl": "https://api.shiningcmusic.com",
    "SessionTimeout": "30",
    "AllowedOrigins": ["https://app.shiningcmusic.com"]
  }
}
```

2. **Startup Configuration**
```csharp
services.AddBffServices(Configuration);
services.AddAuthentication()
        .AddCookie("BFFAuth");
```

## Testing

1. **Unit Tests**
   - Test BFF services in isolation
   - Mock external API calls
   - Verify error handling

2. **Integration Tests**
   - Test full request flow
   - Verify authentication
   - Test error scenarios

## Monitoring

1. **Logging**
   - Request/response logging
   - Error logging
   - Performance metrics

2. **Metrics**
   - Response times
   - Error rates
   - Session statistics

## Deployment

1. **Azure Deployment**
   - Separate BFF and API deployments
   - Configuration management
   - SSL certificate setup

2. **Environment Configuration**
   - Development
   - Staging
   - Production

## References

- [Documentation](../README.md)
- [Access Level Permissions](AccessLevelPermissions.md)
- [API Documentation](../ShiningCMusicApi/README.md)
