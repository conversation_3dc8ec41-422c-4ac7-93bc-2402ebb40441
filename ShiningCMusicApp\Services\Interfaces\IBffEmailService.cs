using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffEmailService
    {
        Task<bool> SendScheduleReadyEmailAsync(int tutorId);
        Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true);

        // New recipient-based methods
        Task<bool> SendTemplateEmailAsync(EmailRecipient recipient, string templateName, Dictionary<string, string>? additionalPlaceholders = null);
        Task<BulkEmailSendResponse?> SendBulkTemplateEmailAsync(List<EmailRecipient> recipients, string templateName, Dictionary<string, string>? globalPlaceholders = null);
    }
}
