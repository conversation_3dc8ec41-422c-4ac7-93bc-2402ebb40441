@using ShiningCMusicApp.Services.Email

<div class="row mb-3">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="bi bi-clock-history"></i> Sending Progress
                </h6>
                
                <!-- Overall Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Overall Progress</span>
                        <span>@Progress?.Completed / @Progress?.Total</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: @(Progress?.PercentComplete ?? 0)%" 
                             aria-valuenow="@(Progress?.PercentComplete ?? 0)" 
                             aria-valuemin="0" aria-valuemax="100">
                            @((Progress?.PercentComplete ?? 0).ToString("F0"))%
                        </div>
                    </div>
                </div>

                <!-- Current Status -->
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="bi bi-envelope"></i> @(Progress?.CurrentStatus ?? "")
                    </small>
                </div>

                <!-- Success/Error Counts -->
                <div class="row text-center">
                    <div class="col-4">
                        <span class="badge bg-success">@(Progress?.SuccessCount ?? 0) Sent</span>
                    </div>
                    <div class="col-4">
                        <span class="badge bg-danger">@(Progress?.ErrorCount ?? 0) Failed</span>
                    </div>
                    <div class="col-4">
                        <span class="badge bg-secondary">@(Progress?.PendingCount ?? 0) Pending</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


