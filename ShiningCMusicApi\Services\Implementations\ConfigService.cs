using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Globalization;

namespace ShiningCMusicApi.Services.Implementations
{
    public class ConfigService : IConfigService
    {
        private readonly string _connectionString;
        private readonly ILogger<ConfigService> _logger;

        public ConfigService(IConfiguration configuration, ILogger<ConfigService> logger)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
            _logger = logger;
        }

        public async Task<IEnumerable<Config>> GetAllConfigsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM Config ORDER BY GroupId, DataType, [Key]";
            return await connection.QueryAsync<Config>(sql);
        }

        public async Task<IEnumerable<ConfigGroup>> GetConfigGroupsAsync()
        {
            var configs = await GetAllConfigsAsync();
            var groups = configs.GroupBy(c => c.GroupId)
                .Select(g => new ConfigGroup
                {
                    GroupId = g.Key,
                    DisplayName = GetGroupDisplayName(g.Key),
                    Description = GetGroupDescription(g.Key),
                    Configs = g.ToList()
                })
                .OrderBy(g => GetGroupOrder(g.GroupId))
                .ToList();

            return groups;
        }

        public async Task<ConfigGroup?> GetConfigGroupAsync(int groupId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM Config WHERE GroupId = @GroupId ORDER BY DataType, [Key]";
            var configs = await connection.QueryAsync<Config>(sql, new { GroupId = groupId });

            if (!configs.Any())
                return null;

            return new ConfigGroup
            {
                GroupId = groupId,
                DisplayName = GetGroupDisplayName(groupId),
                Description = GetGroupDescription(groupId),
                Configs = configs.ToList()
            };
        }

        public async Task<Config?> GetConfigAsync(int configId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM Config WHERE ConfigId = @ConfigId";
            return await connection.QueryFirstOrDefaultAsync<Config>(sql, new { ConfigId = configId });
        }

        public async Task<Config?> GetConfigAsync(int groupId, string key)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM Config WHERE GroupId = @GroupId AND [Key] = @Key";
            return await connection.QueryFirstOrDefaultAsync<Config>(sql, new { GroupId = groupId, Key = key });
        }

        public async Task<string?> GetConfigValueAsync(int groupId, string key)
        {
            var config = await GetConfigAsync(groupId, key);
            return config?.Value;
            // return config?.IsEnabled == true ? config.Value : null;
        }

        public async Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default)
        {
            try
            {
                var value = await GetConfigValueAsync(groupId, key);
                if (string.IsNullOrEmpty(value))
                    return defaultValue;

                var targetType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                var converted = Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture);
                return (T?)converted;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to convert config value for {GroupId}:{Key} to type {Type}. Using default value.", 
                    groupId, key, typeof(T).Name);
                return defaultValue;
            }
        }

        public async Task<bool> UpdateConfigAsync(int configId, string value)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                UPDATE Config 
                SET [Value] = @Value, UpdatedUTC = GETUTCDATE() 
                WHERE ConfigId = @ConfigId";
            
            var rowsAffected = await connection.ExecuteAsync(sql, new { ConfigId = configId, Value = value });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateConfigAsync(int groupId, string key, string value)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                UPDATE Config
                SET [Value] = @Value, UpdatedUTC = GETUTCDATE()
                WHERE GroupId = @GroupId AND [Key] = @Key";

            var rowsAffected = await connection.ExecuteAsync(sql, new { GroupId = groupId, Key = key, Value = value });
            return rowsAffected > 0;
        }

        public async Task<Config> CreateConfigAsync(Config config)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                INSERT INTO Config (GroupId, [Key], [Value], Description, DataType, IsEnabled, Visible, CreatedUTC)
                VALUES (@GroupId, @Key, @Value, @Description, @DataType, @IsEnabled, @Visible, GETUTCDATE());

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, config);
            config.ConfigId = newId;
            return config;
        }

        public async Task<bool> DeleteConfigAsync(int configId)
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "DELETE FROM Config WHERE ConfigId = @ConfigId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { ConfigId = configId });
            return rowsAffected > 0;
        }

        public async Task<bool> IsBackgroundProcessorEnabledAsync(string processorName)
        {
            var enabledValue = await GetConfigValueAsync((int)ConfigGroupId.BackgroundProcessors, $"{processorName}Enabled");
            return bool.TryParse(enabledValue, out var isEnabled) && isEnabled;
        }

        private static string GetGroupDisplayName(int groupId)
        {
            return groupId switch
            {
                (int)ConfigGroupId.General => "General Settings",
                (int)ConfigGroupId.BackgroundProcessors => "Background Processors",
                (int)ConfigGroupId.UI => "User Interface",
                _ => $"Group {groupId}"
            };
        }

        private static string GetGroupDescription(int groupId)
        {
            return groupId switch
            {
                (int)ConfigGroupId.General => "General application settings",
                (int)ConfigGroupId.BackgroundProcessors => "Background service configurations and enable/disable controls",
                (int)ConfigGroupId.UI => "User interface display settings",
                _ => string.Empty
            };
        }

        private static int GetGroupOrder(int groupId)
        {
            return groupId switch
            {
                (int)ConfigGroupId.General => 1,
                (int)ConfigGroupId.BackgroundProcessors => 2,
                (int)ConfigGroupId.UI => 3,
                _ => 999
            };
        }
    }
}
