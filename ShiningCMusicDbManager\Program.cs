﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ShiningCMusicDbManager.Models;
using ShiningCMusicDbManager.Services;
using Spectre.Console;

namespace ShiningCMusicDbManager
{
    /// <summary>
    /// Main program for the Shining C Music Database Manager
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                // Build configuration
                var configuration = BuildConfiguration();

                // Build service provider
                var serviceProvider = BuildServiceProvider(configuration);

                // Get the migration options from the service provider
                using var scope = serviceProvider.CreateScope();
                var migrationOptions = scope.ServiceProvider.GetRequiredService<DatabaseMigrationOptions>();

                // Handle command line arguments
                if (args.Length > 0)
                {
                    await HandleCommandLineAsync(args, serviceProvider);
                }
                else
                {
                    if (migrationOptions.AutoApplyMigrations)
                    {
                        await HandleCommandLineAsync(args, serviceProvider, true);
                    }
                    else
                    {
                        // Run interactive console interface
                        await RunInteractiveAsync(serviceProvider);
                    }
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.WriteException(ex);
                Environment.Exit(1);
            }
        }

        private static IConfiguration BuildConfiguration()
        {
            var environment = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production";

            return new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();
        }

        private static ServiceProvider BuildServiceProvider(IConfiguration configuration)
        {
            var services = new ServiceCollection();

            // Configuration
            services.AddSingleton(configuration);

            // Load and register DatabaseMigrationOptions
            var migrationOptions = new DatabaseMigrationOptions();
            configuration.GetSection("DatabaseMigration").Bind(migrationOptions);
            services.AddSingleton(migrationOptions);

            // Logging
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConfiguration(configuration.GetSection("Logging"));
                builder.AddConsole();
            });

            // Services
            services.AddScoped<IDatabaseMigrationService, DatabaseMigrationService>();
            services.AddScoped<ConsoleInterface>();

            return services.BuildServiceProvider();
        }

        private static async Task RunInteractiveAsync(ServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var consoleInterface = scope.ServiceProvider.GetRequiredService<ConsoleInterface>();

            await consoleInterface.RunAsync();
        }

        private static async Task HandleCommandLineAsync(string[] args, ServiceProvider serviceProvider, bool autoApply = false)
        {
            using var scope = serviceProvider.CreateScope();
            var migrationService = scope.ServiceProvider.GetRequiredService<IDatabaseMigrationService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

            if (args.Length == 0 && autoApply)
            {
                AnsiConsole.MarkupLine("[cyan]Auto-applying all pending migrations...[/]");
                await ApplyAllMigrationsAsync(migrationService, logger);
                return;
            }

            var command = args[0].ToLowerInvariant();

            try
            {
                switch (command)
                {
                    case "init":
                    case "initialize":
                        await InitializeSystemAsync(migrationService, logger);
                        break;

                    case "status":
                        await ShowStatusAsync(migrationService, logger);
                        break;

                    case "apply":
                        if (args.Length > 1)
                        {
                            await ApplySpecificMigrationAsync(migrationService, logger, args[1]);
                        }
                        else
                        {
                            await ApplyAllMigrationsAsync(migrationService, logger);
                        }
                        break;

                    case "rollback":
                        if (args.Length > 1)
                        {
                            await RollbackMigrationAsync(migrationService, logger, args[1]);
                        }
                        else
                        {
                            AnsiConsole.MarkupLine("[red]Error: Script name required for rollback command[/]");
                            ShowUsage();
                        }
                        break;

                    case "validate":
                        await ValidateConnectionAsync(migrationService, logger);
                        break;

                    case "backup":
                        await CreateBackupAsync(migrationService, logger);
                        break;

                    case "discover":
                        await DiscoverScriptsAsync(migrationService, logger);
                        break;

                    case "help":
                    case "--help":
                    case "-h":
                        ShowUsage();
                        break;

                    default:
                        AnsiConsole.MarkupLine($"[red]Unknown command: {command}[/]");
                        ShowUsage();
                        Environment.Exit(1);
                        break;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Command '{Command}' failed", command);
                AnsiConsole.MarkupLine($"[red]Command failed: {ex.Message}[/]");
                Environment.Exit(1);
            }
        }

        private static async Task InitializeSystemAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Initializing database migration system...[/]");

            var success = await migrationService.InitializeMigrationSystemAsync();

            if (success)
            {
                AnsiConsole.MarkupLine("[green]✓ Migration system initialized successfully[/]");
            }
            else
            {
                AnsiConsole.MarkupLine("[red]✗ Migration system initialization failed[/]");
                Environment.Exit(1);
            }
        }

        private static async Task ShowStatusAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Loading migration status...[/]");

            var statuses = await migrationService.GetMigrationStatusAsync();

            if (statuses.Count == 0)
            {
                AnsiConsole.MarkupLine("[yellow]No migration scripts found[/]");
                return;
            }

            var table = new Table();
            table.AddColumn("Script #");
            table.AddColumn("Script Name");
            table.AddColumn("Status");
            table.AddColumn("Applied Date");

            foreach (var status in statuses)
            {
                var statusColor = status.Status switch
                {
                    "Applied" => "green",
                    "Pending" => "yellow",
                    "Changed" => "orange1",
                    "Failed" => "red",
                    _ => "white"
                };

                var appliedDate = status.AppliedDate?.ToString("yyyy-MM-dd HH:mm") ?? "-";

                table.AddRow(
                    status.ScriptNumber.ToString("000"),
                    status.ScriptName,
                    $"[{statusColor}]{status.Status}[/]",
                    appliedDate
                );
            }

            AnsiConsole.Write(table);

            // Summary
            var applied = statuses.Count(s => s.IsApplied);
            var pending = statuses.Count(s => !s.IsApplied);
            var changed = statuses.Count(s => s.HasChanged);

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine($"[green]Applied:[/] {applied} | [yellow]Pending:[/] {pending} | [orange1]Changed:[/] {changed}");
        }

        private static async Task ApplyAllMigrationsAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Applying all pending migrations...[/]");

            var result = await migrationService.ApplyAllMigrationsAsync();

            if (result.Success)
            {
                AnsiConsole.MarkupLine($"[green]✓ {result.Message}[/]");

                if (result.AppliedScripts.Count > 0)
                {
                    AnsiConsole.MarkupLine($"[green]Applied scripts ({result.AppliedScripts.Count}):[/]");
                    foreach (var script in result.AppliedScripts)
                    {
                        AnsiConsole.MarkupLine($"  [green]✓[/] {script}");
                    }
                }
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ {result.Message}[/]");

                if (result.FailedScripts.Count > 0)
                {
                    AnsiConsole.MarkupLine($"[red]Failed scripts ({result.FailedScripts.Count}):[/]");
                    foreach (var script in result.FailedScripts)
                    {
                        AnsiConsole.MarkupLine($"  [red]✗[/] {script}");
                    }
                }

                Environment.Exit(1);
            }
        }

        private static async Task ApplySpecificMigrationAsync(IDatabaseMigrationService migrationService, ILogger _, string scriptName)
        {
            AnsiConsole.MarkupLine($"[cyan]Applying migration: {scriptName}...[/]");

            var result = await migrationService.ApplySpecificMigrationAsync(scriptName);

            if (result.Success)
            {
                AnsiConsole.MarkupLine($"[green]✓ {result.Message}[/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ {result.Message}[/]");
                Environment.Exit(1);
            }
        }

        private static async Task RollbackMigrationAsync(IDatabaseMigrationService migrationService, ILogger _, string scriptName)
        {
            AnsiConsole.MarkupLine($"[cyan]Rolling back migration: {scriptName}...[/]");

            var result = await migrationService.RollbackMigrationAsync(scriptName);

            if (result.Success)
            {
                AnsiConsole.MarkupLine($"[green]✓ {result.Message}[/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ {result.Message}[/]");
                Environment.Exit(1);
            }
        }

        private static async Task ValidateConnectionAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Validating database connection...[/]");

            var isValid = await migrationService.ValidateDatabaseConnectionAsync();

            if (isValid)
            {
                AnsiConsole.MarkupLine("[green]✓ Database connection is valid[/]");
            }
            else
            {
                AnsiConsole.MarkupLine("[red]✗ Database connection failed[/]");
                Environment.Exit(1);
            }
        }

        private static async Task CreateBackupAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Creating database backup...[/]");

            var backupPath = await migrationService.CreateDatabaseBackupAsync();

            if (backupPath != null)
            {
                AnsiConsole.MarkupLine($"[green]✓ Database backup created successfully[/]");
                AnsiConsole.MarkupLine($"[dim]Location: {backupPath}[/]");
            }
            else
            {
                AnsiConsole.MarkupLine("[red]✗ Failed to create database backup[/]");
                Environment.Exit(1);
            }
        }

        private static async Task DiscoverScriptsAsync(IDatabaseMigrationService migrationService, ILogger _)
        {
            AnsiConsole.MarkupLine("[cyan]Discovering SQL scripts...[/]");

            var scripts = await migrationService.DiscoverScriptsAsync();

            if (scripts.Count == 0)
            {
                AnsiConsole.MarkupLine("[yellow]No SQL scripts found[/]");
                return;
            }

            var table = new Table();
            table.AddColumn("Script #");
            table.AddColumn("Script Name");
            table.AddColumn("Category");
            table.AddColumn("Size");

            foreach (var script in scripts)
            {
                var sizeKb = script.FileSize / 1024.0;
                var sizeDisplay = sizeKb < 1 ? $"{script.FileSize} B" : $"{sizeKb:F1} KB";

                table.AddRow(
                    script.ScriptNumber.ToString("000"),
                    script.Name,
                    script.Category.ToString(),
                    sizeDisplay
                );
            }

            AnsiConsole.Write(table);
            AnsiConsole.MarkupLine($"[green]Found {scripts.Count} SQL scripts[/]");
        }

        private static void ShowUsage()
        {
            AnsiConsole.MarkupLine("[yellow]Shining C Music Database Manager[/]");
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[cyan]Usage:[/]");
            AnsiConsole.MarkupLine("  ShiningCMusicDbManager [dim]<command>[/] [dim]<options>[/]");
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[cyan]Commands:[/]");
            AnsiConsole.MarkupLine("  [green]init[/]                    Initialize the migration system");
            AnsiConsole.MarkupLine("  [green]status[/]                  Show migration status");
            AnsiConsole.MarkupLine("  [green]apply[/]                   Apply all pending migrations");
            AnsiConsole.MarkupLine("  [green]apply[/] [dim]<script>[/]          Apply specific migration");
            AnsiConsole.MarkupLine("  [green]rollback[/] [dim]<script>[/]       Rollback specific migration");
            AnsiConsole.MarkupLine("  [green]validate[/]                Validate database connection");
            AnsiConsole.MarkupLine("  [green]backup[/]                  Create database backup");
            AnsiConsole.MarkupLine("  [green]discover[/]                Discover SQL scripts");
            AnsiConsole.MarkupLine("  [green]help[/]                    Show this help message");
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[cyan]Examples:[/]");
            AnsiConsole.MarkupLine("  ShiningCMusicDbManager status");
            AnsiConsole.MarkupLine("  ShiningCMusicDbManager apply");
            AnsiConsole.MarkupLine("  ShiningCMusicDbManager apply script_001_MusicSchoolDB_Create.sql");
            AnsiConsole.MarkupLine("  ShiningCMusicDbManager rollback script_001_MusicSchoolDB_Create.sql");
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Run without arguments for interactive mode[/]");
        }
    }
}
