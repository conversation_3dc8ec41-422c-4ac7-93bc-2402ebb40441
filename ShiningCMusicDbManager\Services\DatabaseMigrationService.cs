using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ShiningCMusicDbManager.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Dapper;

namespace ShiningCMusicDbManager.Services
{
    /// <summary>
    /// Service for managing database migrations in standalone application
    /// </summary>
    public class DatabaseMigrationService : IDatabaseMigrationService
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseMigrationService> _logger;
        private readonly string _sqlScriptsPath;
        private readonly DatabaseMigrationOptions _options;

        public DatabaseMigrationService(IConfiguration configuration, ILogger<DatabaseMigrationService> logger, DatabaseMigrationOptions options)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");

            _logger = logger;
            _options = options ?? throw new ArgumentNullException(nameof(options));

            // Use configured SQL scripts path, with fallback to default
            _sqlScriptsPath = Path.IsPathRooted(_options.SqlScriptsPath)
                ? _options.SqlScriptsPath
                : Path.Combine(Directory.GetCurrentDirectory(), _options.SqlScriptsPath);
        }

        public async Task<bool> InitializeMigrationSystemAsync()
        {
            try
            {
                _logger.LogInformation("Initializing database migration system...");

                // 1. Validate connection
                if (!await ValidateDatabaseConnectionAsync())
                {
                    _logger.LogError("Database connection validation failed");
                    return false;
                }

                // 2. Ensure migration tracking table exists
                await EnsureMigrationTableExistsAsync();

                _logger.LogInformation("Database migration system initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize migration system");
                return false;
            }
        }

        public async Task<MigrationResult> ApplyAllMigrationsAsync()
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Starting database migration process...");

                // 1. Validate connection
                if (!await ValidateDatabaseConnectionAsync())
                {
                    result.Success = false;
                    result.Message = "Database connection validation failed";
                    return result;
                }

                // 2. Ensure migration tracking table exists
                await EnsureMigrationTableExistsAsync();

                // 3. Discover and order scripts
                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                // 4. Filter scripts that need to be applied (only scripts never applied before)
                var scriptsToApply = scripts.Where(s =>
                    !appliedMigrations.ContainsKey(s.Name)
                ).OrderBy(s => s.ScriptNumber).ThenBy(s => s.ExecutionOrder).ToList();

                // Note: If a script that was previously applied has changed content, we skip it
                // and surface a warning so it's explicit but non-blocking.
                var changedScripts = scripts.Where(s =>
                    appliedMigrations.ContainsKey(s.Name) && appliedMigrations[s.Name].ScriptHash != s.Hash
                ).Select(s => s.Name).ToList();
                if (changedScripts.Any())
                {
                    result.Warnings.Add($"Changed scripts already applied and skipped: {string.Join(", ", changedScripts)}");
                }

                if (!scriptsToApply.Any())
                {
                    result.Success = true;
                    result.Message = "All migrations are up to date";
                    _logger.LogInformation("All migrations are up to date");
                    return result;
                }

                _logger.LogInformation($"Found {scriptsToApply.Count} migrations to apply");

                // 5. Validate dependencies if configured
                if (_options.ValidateDependencies)
                {
                    var dependencyIssues = await ValidateDependenciesAsync();
                    if (dependencyIssues.Any())
                    {
                        result.Success = false;
                        result.Message = "Dependency validation failed";
                        result.Warnings.AddRange(dependencyIssues);
                        return result;
                    }
                }

                // 6. Create backup if configured
                if (_options.BackupBeforeMigration)
                {
                    var backupPath = await CreateDatabaseBackupAsync();
                    if (backupPath != null)
                    {
                        _logger.LogInformation($"Database backup created: {backupPath}");
                    }
                }

                // 7. Apply migrations in order
                foreach (var script in scriptsToApply)
                {
                    var scriptResult = await ApplyScriptAsync(script, appliedMigrations.ContainsKey(script.Name));
                    result.ScriptResults.Add(scriptResult);

                    if (scriptResult.Success)
                    {
                        result.AppliedScripts.Add(script.Name);
                        if (_options.ShowExecutionTime)
                        {
                            _logger.LogInformation($"✓ Applied migration: {script.Name} ({scriptResult.ExecutionTime.TotalMilliseconds:F0}ms)");
                        }
                        else
                        {
                            _logger.LogInformation($"✓ Applied migration: {script.Name}");
                        }
                    }
                    else
                    {
                        result.FailedScripts.Add(script.Name);
                        result.Warnings.Add($"{script.Name}: {scriptResult.ErrorMessage}");
                        _logger.LogError($"✗ Failed to apply migration: {script.Name} - {scriptResult.ErrorMessage}");

                        // Decide whether to continue or stop on failure
                        if (script.IsCritical || _options.StopOnFirstFailure)
                        {
                            result.Success = false;
                            result.Message = $"Critical migration failed: {script.Name}";
                            return result;
                        }
                    }
                }

                result.Success = result.FailedScripts.Count == 0;
                result.Message = result.Success
                    ? $"Successfully applied {result.AppliedScripts.Count} migrations"
                    : $"Applied {result.AppliedScripts.Count} migrations with {result.FailedScripts.Count} failures";

                _logger.LogInformation(result.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Migration process failed");
                result.Success = false;
                result.Message = $"Migration process failed: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
                if (_options.ShowExecutionTime)
                {
                    _logger.LogInformation($"Migration process completed in {stopwatch.Elapsed.TotalSeconds:F2} seconds");
                }
            }

            return result;
        }

        public async Task<bool> ValidateDatabaseConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.QuerySingleAsync<int>("SELECT 1");

                _logger.LogDebug("Database connection validation successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection validation failed");
                return false;
            }
        }

        public async Task<List<SqlScript>> DiscoverScriptsAsync()
        {
            var scripts = new List<SqlScript>();

            if (!Directory.Exists(_sqlScriptsPath))
            {
                _logger.LogWarning($"SQL scripts directory not found: {_sqlScriptsPath}");
                return scripts;
            }

            var scriptFiles = Directory.GetFiles(_sqlScriptsPath, "*.sql")
                .Where(f => !Path.GetFileName(f).StartsWith("rollback_", StringComparison.OrdinalIgnoreCase))
                .Where(f => !Path.GetFileName(f).StartsWith("test_", StringComparison.OrdinalIgnoreCase))
                .Where(f => !Path.GetFileName(f).StartsWith("verify_", StringComparison.OrdinalIgnoreCase))
                .ToList();

            foreach (var file in scriptFiles)
            {
                try
                {
                    var fileInfo = new FileInfo(file);
                    var content = await File.ReadAllTextAsync(file);
                    var fileName = Path.GetFileName(file);

                    var script = new SqlScript
                    {
                        Name = fileName,
                        Content = content,
                        Hash = ComputeHash(content),
                        ScriptNumber = ExtractScriptNumber(fileName),
                        ExecutionOrder = GetExecutionOrder(fileName),
                        FilePath = file,
                        FileSize = fileInfo.Length,
                        LastModified = fileInfo.LastWriteTime,
                        Category = DetermineScriptCategory(fileName),
                        IsCritical = IsCriticalScript(fileName),
                        CommandTimeoutSeconds = _options.CommandTimeoutSeconds,
                        Description = ExtractScriptDescription(content),
                        // Disable transaction for scripts that require autocommit (e.g., CREATE DATABASE)
                        UseTransaction = !ContentRequiresAutocommit(content)
                    };

                    scripts.Add(script);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing script file: {file}");
                }
            }

            _logger.LogInformation($"Discovered {scripts.Count} migration scripts");
            return scripts.OrderBy(s => s.ScriptNumber).ThenBy(s => s.ExecutionOrder).ToList();
        }

        private bool ContentRequiresAutocommit(string content)
        {
            // Heuristics: certain statements are not allowed inside user transactions
            var patterns = new[]
            {
                @"\bCREATE\s+DATABASE\b",
                @"\bALTER\s+DATABASE\b",
                @"\bRESTORE\s+DATABASE\b",
                @"\bBACKUP\s+DATABASE\b"
            };
            foreach (var pattern in patterns)
            {
                if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                {
                    return true;
                }
            }
            return false;
        }

        private int ExtractScriptNumber(string fileName)
        {
            // Extract number from script_XXX_... format
            var match = Regex.Match(fileName, @"script_(\d+)", RegexOptions.IgnoreCase);
            if (match.Success && int.TryParse(match.Groups[1].Value, out int number))
            {
                return number;
            }

            // Fallback to default order for non-numbered scripts
            return 999999; // Put non-numbered scripts at the end
        }

        private async Task EnsureMigrationTableExistsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);

                // Check if table exists
                var checkTableSql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = 'dbo'
                    AND TABLE_NAME = 'DatabaseMigrations'";

                var tableCount = await connection.QuerySingleAsync<int>(checkTableSql);
                var tableExists = tableCount > 0;

                if (!tableExists)
                {
                    _logger.LogInformation("Creating DatabaseMigrations table...");
                    await CreateBasicMigrationTableAsync(connection);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ensure migration table exists");
                throw;
            }
        }

        private async Task CreateBasicMigrationTableAsync(SqlConnection connection)
        {
            var createTableSql = @"
                CREATE TABLE [dbo].[DatabaseMigrations] (
                    [Id] INT IDENTITY(1,1) NOT NULL,
                    [ScriptName] NVARCHAR(255) NOT NULL,
                    [AppliedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Success',
                    [ExecutionTimeMs] BIGINT NULL,
                    [ErrorMessage] NVARCHAR(MAX) NULL,
                    [ScriptHash] NVARCHAR(64) NOT NULL,
                    [AppliedBy] NVARCHAR(100) NULL DEFAULT 'DbManager',
                    [ScriptCategory] NVARCHAR(50) NULL,
                    [BatchesExecuted] INT NULL DEFAULT 0,
                    [IsRollback] BIT NOT NULL DEFAULT 0,
                    [RolledBackDate] DATETIME2 NULL,
                    [RolledBackBy] NVARCHAR(100) NULL,
                    [Dependencies] NVARCHAR(500) NULL,
                    [FileSize] BIGINT NULL,
                    [LastModified] DATETIME2 NULL,
                    [Notes] NVARCHAR(1000) NULL,
                    [ScriptNumber] INT NULL,

                    CONSTRAINT [PK_DatabaseMigrations] PRIMARY KEY CLUSTERED ([Id] ASC),
                    CONSTRAINT [UQ_DatabaseMigrations_ScriptName] UNIQUE NONCLUSTERED ([ScriptName] ASC)
                );

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_AppliedDate]
                ON [dbo].[DatabaseMigrations] ([AppliedDate] DESC);

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_Status]
                ON [dbo].[DatabaseMigrations] ([Status] ASC);

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_ScriptHash]
                ON [dbo].[DatabaseMigrations] ([ScriptHash] ASC);

                CREATE NONCLUSTERED INDEX [IX_DatabaseMigrations_ScriptNumber]
                ON [dbo].[DatabaseMigrations] ([ScriptNumber] ASC);";

            await connection.ExecuteAsync(createTableSql, commandTimeout: _options.CommandTimeoutSeconds);

            _logger.LogInformation("Created DatabaseMigrations table with indexes");
        }

        private async Task<Dictionary<string, (string ScriptHash, DateTime AppliedDate)>> GetAppliedMigrationsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);

                var sql = @"
                    SELECT [ScriptName], [ScriptHash], [AppliedDate]
                    FROM [dbo].[DatabaseMigrations]
                    WHERE [Status] = 'Success' AND [RolledBackDate] IS NULL";

                var results = await connection.QueryAsync<(string ScriptName, string ScriptHash, DateTime AppliedDate)>(sql);

                return results.ToDictionary(
                    r => r.ScriptName,
                    r => (r.ScriptHash, r.AppliedDate)
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get applied migrations");
                throw;
            }
        }

        private async Task<ScriptExecutionResult> ApplyScriptAsync(SqlScript script, bool isUpdate)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ScriptExecutionResult
            {
                ScriptName = script.Name,
                IsUpdate = isUpdate
            };

            try
            {
                _logger.LogDebug($"Applying migration: {script.Name}");

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Split script by GO statements and execute each batch
                var batches = SplitSqlScript(script.Content);
                result.BatchesExecuted = batches.Count;

                if (_options.UseTransactions && script.UseTransaction)
                {
                    using var transaction = connection.BeginTransaction();
                    try
                    {
                        foreach (var batch in batches)
                        {
                            if (string.IsNullOrWhiteSpace(batch)) continue;

                            using var command = new SqlCommand(batch, connection, transaction);
                            command.CommandTimeout = script.CommandTimeoutSeconds;
                            await command.ExecuteNonQueryAsync();
                        }

                        // Record successful migration
                        await RecordMigrationAsync(connection, transaction, script, "Success", stopwatch.ElapsedMilliseconds, null);

                        await transaction.CommitAsync();
                        result.Success = true;
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        await RecordMigrationAsync(connection, null, script, "Failed", stopwatch.ElapsedMilliseconds, ex.Message);
                        result.Success = false;
                        result.ErrorMessage = ex.Message;
                    }
                }
                else
                {
                    // Execute without transaction
                    try
                    {
                        foreach (var batch in batches)
                        {
                            if (string.IsNullOrWhiteSpace(batch)) continue;

                            using var command = new SqlCommand(batch, connection);
                            command.CommandTimeout = script.CommandTimeoutSeconds;
                            await command.ExecuteNonQueryAsync();
                        }

                        await RecordMigrationAsync(connection, null, script, "Success", stopwatch.ElapsedMilliseconds, null);
                        result.Success = true;
                    }
                    catch (Exception ex)
                    {
                        await RecordMigrationAsync(connection, null, script, "Failed", stopwatch.ElapsedMilliseconds, ex.Message);
                        result.Success = false;
                        result.ErrorMessage = ex.Message;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error applying migration: {script.Name}");
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        private async Task RecordMigrationAsync(SqlConnection connection, SqlTransaction? transaction, SqlScript script, string status, long executionTimeMs, string? errorMessage)
        {
            try
            {
                var sql = @"
                    IF EXISTS (SELECT 1 FROM [dbo].[DatabaseMigrations] WHERE [ScriptName] = @ScriptName)
                    BEGIN
                        UPDATE [dbo].[DatabaseMigrations]
                        SET [AppliedDate] = GETUTCDATE(),
                            [Status] = @Status,
                            [ExecutionTimeMs] = @ExecutionTimeMs,
                            [ErrorMessage] = @ErrorMessage,
                            [ScriptHash] = @ScriptHash,
                            [AppliedBy] = @AppliedBy,
                            [ScriptCategory] = @ScriptCategory,
                            [BatchesExecuted] = @BatchesExecuted,
                            [FileSize] = @FileSize,
                            [LastModified] = @LastModified,
                            [Notes] = @Notes,
                            [ScriptNumber] = @ScriptNumber
                        WHERE [ScriptName] = @ScriptName
                    END
                    ELSE
                    BEGIN
                        INSERT INTO [dbo].[DatabaseMigrations]
                        ([ScriptName], [Status], [ExecutionTimeMs], [ErrorMessage], [ScriptHash], [AppliedBy],
                         [ScriptCategory], [BatchesExecuted], [FileSize], [LastModified], [Notes], [ScriptNumber])
                        VALUES
                        (@ScriptName, @Status, @ExecutionTimeMs, @ErrorMessage, @ScriptHash, @AppliedBy,
                         @ScriptCategory, @BatchesExecuted, @FileSize, @LastModified, @Notes, @ScriptNumber)
                    END";

                var parameters = new
                {
                    ScriptName = script.Name,
                    Status = status,
                    ExecutionTimeMs = executionTimeMs,
                    ErrorMessage = errorMessage,
                    ScriptHash = script.Hash,
                    AppliedBy = "DatabaseManager",
                    ScriptCategory = script.Category.ToString(),
                    BatchesExecuted = SplitSqlScript(script.Content).Count,
                    FileSize = script.FileSize,
                    LastModified = script.LastModified,
                    Notes = script.Description,
                    ScriptNumber = script.ScriptNumber
                };

                await connection.ExecuteAsync(sql, parameters, transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to record migration: {script.Name}");
                // Don't throw here as it would rollback the actual migration
            }
        }

        private List<string> SplitSqlScript(string script)
        {
            var batches = new List<string>();

            // Split by GO statements (case insensitive, must be on its own line)
            var goPattern = @"^\s*GO\s*$";
            var lines = script.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
            var currentBatch = new StringBuilder();

            foreach (var line in lines)
            {
                if (Regex.IsMatch(line, goPattern, RegexOptions.IgnoreCase))
                {
                    if (currentBatch.Length > 0)
                    {
                        batches.Add(currentBatch.ToString().Trim());
                        currentBatch.Clear();
                    }
                }
                else
                {
                    currentBatch.AppendLine(line);
                }
            }

            // Add the last batch if it has content
            if (currentBatch.Length > 0)
            {
                batches.Add(currentBatch.ToString().Trim());
            }

            return batches.Where(b => !string.IsNullOrWhiteSpace(b)).ToList();
        }

        private string ComputeHash(string content)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(content));
            return Convert.ToHexString(hash);
        }

        private int GetExecutionOrder(string fileName)
        {
            // First check for script number prefix
            var match = Regex.Match(fileName, @"script_(\d+)", RegexOptions.IgnoreCase);
            if (match.Success && int.TryParse(match.Groups[1].Value, out int number))
            {
                return number;
            }

            // Fallback to legacy naming patterns
            var orderMap = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase)
            {
                { "MusicSchoolDB Create.sql", 1 },
                { "MusicSchoolDB Objects.sql", 2 },
                { "Add_DatabaseMigrations_Table.sql", 5 },
                { "Add_OpenIddict_Tables.sql", 10 },
                { "Add_ClientSecrets_Table.sql", 11 },
                { "Migrate_Clients_To_OpenIddict.sql", 12 },
                { "Sample Data.sql", 1000 } // Always last
            };

            if (orderMap.ContainsKey(fileName))
                return orderMap[fileName];

            // Default ordering patterns
            if (fileName.StartsWith("Add_", StringComparison.OrdinalIgnoreCase))
                return 100;

            if (fileName.StartsWith("Update_", StringComparison.OrdinalIgnoreCase))
                return 200;

            if (fileName.StartsWith("Migrate_", StringComparison.OrdinalIgnoreCase))
                return 300;

            if (fileName.Contains("Config", StringComparison.OrdinalIgnoreCase))
                return 400;

            // Default ordering for other scripts
            return 500;
        }

        private ScriptCategory DetermineScriptCategory(string fileName)
        {
            if (fileName.Contains("Create", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.DatabaseCreation;

            if (fileName.StartsWith("Add_", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Schema;

            if (fileName.StartsWith("Update_", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Schema;

            if (fileName.Contains("Data", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("Sample", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Data;

            if (fileName.Contains("Index", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Index;

            if (fileName.Contains("sp_", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("Procedure", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Procedures;

            if (fileName.Contains("Config", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Configuration;

            if (fileName.Contains("Security", StringComparison.OrdinalIgnoreCase) ||
                fileName.Contains("User", StringComparison.OrdinalIgnoreCase))
                return ScriptCategory.Security;

            return ScriptCategory.Unknown;
        }

        private bool IsCriticalScript(string fileName)
        {
            var criticalScripts = new[]
            {
                "script_001_", // Database creation
                "script_002_", // Core objects
                "MusicSchoolDB Create.sql",
                "MusicSchoolDB Objects.sql",
                "Add_DatabaseMigrations_Table.sql",
                "Add_OpenIddict_Tables.sql"
            };

            return criticalScripts.Any(pattern => fileName.StartsWith(pattern, StringComparison.OrdinalIgnoreCase));
        }

        private string? ExtractScriptDescription(string content)
        {
            // Look for description in comments at the top of the file
            var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines.Take(20)) // Only check first 20 lines
            {
                var trimmed = line.Trim();
                if (trimmed.StartsWith("-- Purpose:", StringComparison.OrdinalIgnoreCase) ||
                    trimmed.StartsWith("Purpose:", StringComparison.OrdinalIgnoreCase))
                {
                    return trimmed.Substring(trimmed.IndexOf(':') + 1).Trim();
                }

                if (trimmed.StartsWith("-- DESCRIPTION:", StringComparison.OrdinalIgnoreCase) ||
                    trimmed.StartsWith("DESCRIPTION:", StringComparison.OrdinalIgnoreCase))
                {
                    return trimmed.Substring(trimmed.IndexOf(':') + 1).Trim();
                }
            }

            return null;
        }

        // Implement remaining interface methods
        public async Task<MigrationResult> ApplySpecificMigrationAsync(string scriptName)
        {
            var result = new MigrationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation($"Applying specific migration: {scriptName}");

                if (!await ValidateDatabaseConnectionAsync())
                {
                    result.Success = false;
                    result.Message = "Database connection validation failed";
                    return result;
                }

                await EnsureMigrationTableExistsAsync();

                var scripts = await DiscoverScriptsAsync();
                var script = scripts.FirstOrDefault(s => s.Name.Equals(scriptName, StringComparison.OrdinalIgnoreCase));

                if (script == null)
                {
                    result.Success = false;
                    result.Message = $"Script not found: {scriptName}";
                    return result;
                }

                var appliedMigrations = await GetAppliedMigrationsAsync();
                var isUpdate = appliedMigrations.ContainsKey(script.Name);

                var scriptResult = await ApplyScriptAsync(script, isUpdate);
                result.ScriptResults.Add(scriptResult);

                if (scriptResult.Success)
                {
                    result.AppliedScripts.Add(script.Name);
                    result.Success = true;
                    result.Message = $"Successfully applied migration: {scriptName}";
                }
                else
                {
                    result.FailedScripts.Add(script.Name);
                    result.Success = false;
                    result.Message = $"Failed to apply migration: {scriptName} - {scriptResult.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error applying specific migration: {scriptName}");
                result.Success = false;
                result.Message = $"Error applying migration: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        public async Task<List<MigrationStatus>> GetMigrationStatusAsync()
        {
            var statuses = new List<MigrationStatus>();

            try
            {
                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                foreach (var script in scripts)
                {
                    var status = new MigrationStatus
                    {
                        ScriptName = script.Name,
                        ScriptNumber = script.ScriptNumber,
                        CurrentHash = script.Hash,
                        ExecutionOrder = script.ExecutionOrder,
                        IsRollback = script.IsRollback
                    };

                    if (appliedMigrations.ContainsKey(script.Name))
                    {
                        var applied = appliedMigrations[script.Name];
                        status.IsApplied = true;
                        status.AppliedDate = applied.AppliedDate;
                        status.AppliedHash = applied.ScriptHash;
                        status.HasChanged = applied.ScriptHash != script.Hash;
                        status.Status = status.HasChanged ? "Changed" : "Applied";
                    }
                    else
                    {
                        status.IsApplied = false;
                        status.Status = "Pending";
                    }

                    statuses.Add(status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get migration status");
            }

            return statuses.OrderBy(s => s.ScriptNumber).ThenBy(s => s.ExecutionOrder).ToList();
        }

        public async Task<MigrationResult> RollbackMigrationAsync(string scriptName)
        {
            var result = new MigrationResult();

            try
            {
                _logger.LogInformation($"Rolling back migration: {scriptName}");

                // Look for rollback script
                var rollbackScriptName = $"rollback_{scriptName}";
                var scripts = await DiscoverScriptsAsync();
                var rollbackScript = scripts.FirstOrDefault(s => s.Name.Equals(rollbackScriptName, StringComparison.OrdinalIgnoreCase));

                if (rollbackScript == null)
                {
                    result.Success = false;
                    result.Message = $"Rollback script not found: {rollbackScriptName}";
                    return result;
                }

                var scriptResult = await ApplyScriptAsync(rollbackScript, false);
                result.ScriptResults.Add(scriptResult);

                if (scriptResult.Success)
                {
                    // Mark original migration as rolled back
                    await MarkMigrationAsRolledBackAsync(scriptName);

                    result.AppliedScripts.Add(rollbackScript.Name);
                    result.Success = true;
                    result.Message = $"Successfully rolled back migration: {scriptName}";
                }
                else
                {
                    result.FailedScripts.Add(rollbackScript.Name);
                    result.Success = false;
                    result.Message = $"Failed to rollback migration: {scriptName} - {scriptResult.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error rolling back migration: {scriptName}");
                result.Success = false;
                result.Message = $"Error rolling back migration: {ex.Message}";
            }

            return result;
        }

        private async Task MarkMigrationAsRolledBackAsync(string scriptName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);

                var sql = @"
                    UPDATE [dbo].[DatabaseMigrations]
                    SET [RolledBackDate] = GETUTCDATE(),
                        [RolledBackBy] = 'DatabaseManager'
                    WHERE [ScriptName] = @ScriptName";

                await connection.ExecuteAsync(sql, new { ScriptName = scriptName });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to mark migration as rolled back: {scriptName}");
            }
        }

        public async Task<MigrationResult> ForceApplyMigrationAsync(string scriptName)
        {
            // Same as ApplySpecificMigrationAsync but ignores hash changes
            return await ApplySpecificMigrationAsync(scriptName);
        }

        public async Task<MigrationStatus?> GetMigrationDetailsAsync(string scriptName)
        {
            var statuses = await GetMigrationStatusAsync();
            return statuses.FirstOrDefault(s => s.ScriptName.Equals(scriptName, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<List<string>> ValidateDependenciesAsync()
        {
            var issues = new List<string>();

            try
            {
                var scripts = await DiscoverScriptsAsync();
                var appliedMigrations = await GetAppliedMigrationsAsync();

                foreach (var script in scripts.Where(s => s.Dependencies.Any()))
                {
                    foreach (var dependency in script.Dependencies)
                    {
                        if (!appliedMigrations.ContainsKey(dependency))
                        {
                            issues.Add($"Script '{script.Name}' depends on '{dependency}' which has not been applied");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate dependencies");
                issues.Add($"Dependency validation failed: {ex.Message}");
            }

            return issues;
        }

        public async Task<string?> CreateDatabaseBackupAsync()
        {
            try
            {
                if (!Directory.Exists(_options.BackupDirectory))
                {
                    Directory.CreateDirectory(_options.BackupDirectory);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"MusicSchool_Backup_{timestamp}.bak";
                var backupPath = Path.Combine(_options.BackupDirectory, backupFileName);

                using var connection = new SqlConnection(_connectionString);

                var sql = $@"
                    BACKUP DATABASE [MusicSchool]
                    TO DISK = '{backupPath}'
                    WITH FORMAT, INIT, COMPRESSION";

                await connection.ExecuteAsync(sql, commandTimeout: 600); // 10 minutes for backup

                _logger.LogInformation("Database backup created: {BackupPath}", backupPath);
                return backupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create database backup");
                return null;
            }
        }

        public async Task<bool> MarkMigrationAsAppliedAsync(string scriptName, string appliedBy)
        {
            try
            {
                var scripts = await DiscoverScriptsAsync();
                var script = scripts.FirstOrDefault(s => s.Name.Equals(scriptName, StringComparison.OrdinalIgnoreCase));

                if (script == null)
                {
                    _logger.LogError("Script not found: {ScriptName}", scriptName);
                    return false;
                }

                using var connection = new SqlConnection(_connectionString);

                await RecordMigrationAsync(connection, null, script, "Success", 0, null);

                _logger.LogInformation("Marked migration as applied: {ScriptName}", scriptName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark migration as applied: {ScriptName}", scriptName);
                return false;
            }
        }

        public async Task<bool> RemoveMigrationRecordAsync(string scriptName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);

                var sql = "DELETE FROM [dbo].[DatabaseMigrations] WHERE [ScriptName] = @ScriptName";

                var rowsAffected = await connection.ExecuteAsync(sql, new { ScriptName = scriptName });

                if (rowsAffected > 0)
                {
                    _logger.LogInformation("Removed migration record: {ScriptName}", scriptName);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Migration record not found: {ScriptName}", scriptName);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove migration record: {ScriptName}", scriptName);
                return false;
            }
        }
    }


